import { $, Context, Logger } from "koishi";
import { WorldStateConfig } from "./config";

/**
 * 数据清理服务
 * 负责管理数据生命周期，清理过期数据，优化存储空间
 */
export class DataCleanupService {
    private logger: Logger;

    constructor(private ctx: Context, private config: WorldStateConfig) {
        this.logger = ctx.logger("worldstate:cleanup");
    }

    /**
     * 执行完整的数据清理流程
     */
    async performFullCleanup(): Promise<CleanupResult> {
        this.logger.info("开始执行完整数据清理");
        const startTime = Date.now();

        const result: CleanupResult = {
            startTime: new Date(),
            endTime: null,
            totalProcessed: 0,
            totalDeleted: 0,
            details: {
                turns: { processed: 0, deleted: 0 },
                events: { processed: 0, deleted: 0 },
                responses: { processed: 0, deleted: 0 },
                members: { processed: 0, deleted: 0 },
                orphaned: { processed: 0, deleted: 0 },
            },
            errors: [],
        };

        try {
            // 1. 清理过期回合
            const turnResult = await this.cleanupExpiredTurns();
            result.details.turns = turnResult;
            result.totalProcessed += turnResult.processed;
            result.totalDeleted += turnResult.deleted;

            // 2. 清理孤立事件
            const eventResult = await this.cleanupOrphanedEvents();
            result.details.events = eventResult;
            result.totalProcessed += eventResult.processed;
            result.totalDeleted += eventResult.deleted;

            // 3. 清理孤立响应
            const responseResult = await this.cleanupOrphanedResponses();
            result.details.responses = responseResult;
            result.totalProcessed += responseResult.processed;
            result.totalDeleted += responseResult.deleted;

            // 4. 清理无效成员
            const memberResult = await this.cleanupInvalidMembers();
            result.details.members = memberResult;
            result.totalProcessed += memberResult.processed;
            result.totalDeleted += memberResult.deleted;

            // 5. 清理其他孤立数据
            const orphanedResult = await this.cleanupOrphanedData();
            result.details.orphaned = orphanedResult;
            result.totalProcessed += orphanedResult.processed;
            result.totalDeleted += orphanedResult.deleted;
        } catch (error) {
            this.logger.error("数据清理过程中发生错误:", error);
            result.errors.push({
                stage: "general",
                error: error.message,
                timestamp: new Date(),
            });
        }

        result.endTime = new Date();
        const duration = Date.now() - startTime;

        this.logger.info(`数据清理完成，耗时 ${duration}ms，处理 ${result.totalProcessed} 条记录，删除 ${result.totalDeleted} 条记录`);

        return result;
    }

    /**
     * 清理过期回合
     */
    async cleanupExpiredTurns(): Promise<CleanupStageResult> {
        this.logger.debug("开始清理过期回合");

        const retentionDate = new Date(Date.now() - this.config.DataCleanup.RetentionDays * 24 * 60 * 60 * 1000);

        let processed = 0;
        let deleted = 0;
        const batchSize = this.config.DataCleanup.BatchSize;

        while (true) {
            try {
                // 分批获取过期回合
                const expiredTurns = await this.ctx.database
                    .select("turns")
                    .where((row) => $.and($.lt(row.endTimestamp, retentionDate)))
                    .limit(batchSize)
                    .execute();

                if (expiredTurns.length === 0) {
                    break;
                }

                processed += expiredTurns.length;
                const turnIds = expiredTurns.map((t) => t.id);

                // 删除相关的事件数据
                await this.ctx.database.remove("channel_events", { turnId: turnIds });

                // 删除相关的响应数据
                await this.ctx.database.remove("agent_responses", { turnId: turnIds });

                // 删除回合本身
                const deleteResult = await this.ctx.database.remove("turns", { id: turnIds });
                deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;

                this.logger.debug(`批次清理完成：处理 ${expiredTurns.length} 个回合，删除 ${deleteResult.removed} 个`);
            } catch (error) {
                this.logger.error("清理过期回合时发生错误:", error);
                break;
            }
        }

        this.logger.info(`过期回合清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
        return { processed, deleted };
    }

    /**
     * 清理孤立事件（没有对应回合的事件）
     */
    async cleanupOrphanedEvents(): Promise<CleanupStageResult> {
        this.logger.debug("开始清理孤立事件");

        let processed = 0;
        let deleted = 0;
        const batchSize = this.config.DataCleanup.BatchSize;

        while (true) {
            try {
                // 简化查找孤立事件 - 分批获取事件，然后检查对应的回合是否存在
                const events = await this.ctx.database.select("channel_events").limit(batchSize).offset(processed).execute();

                if (events.length === 0) {
                    break;
                }

                // 检查这些事件对应的回合是否存在
                const turnIds = events.map((e) => e.turnId);
                const existingTurns = await this.ctx.database.get("turns", { id: turnIds });
                const existingTurnIds = new Set(existingTurns.map((t) => t.id));

                // 找出孤立的事件
                const orphanedEvents = events.filter((e) => !existingTurnIds.has(e.turnId));

                if (orphanedEvents.length > 0) {
                    const eventIds = orphanedEvents.map((e) => e.id);
                    const deleteResult = await this.ctx.database.remove("channel_events", { id: eventIds });
                    deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
                }

                processed += events.length;

                this.logger.debug(`批次清理孤立事件完成：处理 ${events.length} 个，删除 ${orphanedEvents.length} 个`);
            } catch (error) {
                this.logger.error("清理孤立事件时发生错误:", error);
                break;
            }
        }

        this.logger.info(`孤立事件清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
        return { processed, deleted };
    }

    /**
     * 清理孤立响应（没有对应回合的响应）
     */
    async cleanupOrphanedResponses(): Promise<CleanupStageResult> {
        this.logger.debug("开始清理孤立响应");

        let processed = 0;
        let deleted = 0;
        const batchSize = this.config.DataCleanup.BatchSize;

        while (true) {
            try {
                // 简化查找孤立响应
                const responses = await this.ctx.database.select("agent_responses").limit(batchSize).offset(processed).execute();

                if (responses.length === 0) {
                    break;
                }

                // 检查这些响应对应的回合是否存在
                const turnIds = responses.map((r) => r.turnId);
                const existingTurns = await this.ctx.database.get("turns", { id: turnIds });
                const existingTurnIds = new Set(existingTurns.map((t) => t.id));

                // 找出孤立的响应
                const orphanedResponses = responses.filter((r) => !existingTurnIds.has(r.turnId));

                if (orphanedResponses.length > 0) {
                    const responseIds = orphanedResponses.map((r) => r.id);
                    const deleteResult = await this.ctx.database.remove("agent_responses", { id: responseIds });
                    deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
                }

                processed += responses.length;

                this.logger.debug(`批次清理孤立响应完成：处理 ${responses.length} 个，删除 ${orphanedResponses.length} 个`);
            } catch (error) {
                this.logger.error("清理孤立响应时发生错误:", error);
                break;
            }
        }

        this.logger.info(`孤立响应清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
        return { processed, deleted };
    }

    /**
     * 清理无效成员（对应用户或频道不存在的成员记录）
     */
    async cleanupInvalidMembers(): Promise<CleanupStageResult> {
        this.logger.debug("开始清理无效成员");

        let processed = 0;
        let deleted = 0;
        const batchSize = this.config.DataCleanup.BatchSize;

        while (true) {
            try {
                // 简化查找无效成员
                const members = await this.ctx.database.select("members").limit(batchSize).offset(processed).execute();

                if (members.length === 0) {
                    break;
                }

                // 检查这些成员对应的用户是否存在
                const userIds = members.map((m) => m.userId);
                const existingUsers = await this.ctx.database.get("user", { id: userIds });
                const existingUserIds = new Set(existingUsers.map((u) => u.id));

                // 找出无效的成员
                const invalidMembers = members.filter((m) => !existingUserIds.has(m.userId));

                let batchDeleted = 0;
                for (const member of invalidMembers) {
                    const deleteResult = await this.ctx.database.remove("members", {
                        userId: member.userId,
                        platform: member.platform,
                        channelId: member.channelId,
                    });
                    batchDeleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
                }

                processed += members.length;
                deleted += batchDeleted;

                this.logger.debug(`批次清理无效成员完成：处理 ${members.length} 个，删除 ${batchDeleted} 个`);
            } catch (error) {
                this.logger.error("清理无效成员时发生错误:", error);
                break;
            }
        }

        this.logger.info(`无效成员清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
        return { processed, deleted };
    }

    /**
     * 清理其他孤立数据
     */
    async cleanupOrphanedData(): Promise<CleanupStageResult> {
        this.logger.debug("开始清理其他孤立数据");

        let processed = 0;
        let deleted = 0;

        try {
            // 清理无效的频道记录（没有任何成员或回合的频道）
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const emptyChannels = await this.ctx.database
                .select("channel")
                .where((row) => $.lt(row.lastActivityAt, thirtyDaysAgo))
                .execute();

            // 过滤出真正没有成员和回合的频道
            const filteredEmptyChannels = [];
            for (const channel of emptyChannels) {
                const [members, turns] = await Promise.all([
                    this.ctx.database.get("members", { channelId: channel.id, platform: channel.platform }),
                    this.ctx.database.get("turns", { channelId: channel.id, platform: channel.platform }),
                ]);

                if (members.length === 0 && turns.length === 0) {
                    filteredEmptyChannels.push(channel);
                }
            }

            if (filteredEmptyChannels.length > 0) {
                processed += filteredEmptyChannels.length;

                for (const channel of filteredEmptyChannels) {
                    const deleteResult = await this.ctx.database.remove("channel", {
                        id: channel.id,
                        platform: channel.platform,
                    });
                    deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
                }

                this.logger.debug(`清理空闲频道：处理 ${filteredEmptyChannels.length} 个，删除 ${deleted} 个`);
            }
        } catch (error) {
            this.logger.error("清理孤立数据时发生错误:", error);
        }

        this.logger.info(`孤立数据清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
        return { processed, deleted };
    }

    /**
     * 获取数据库统计信息
     */
    async getDatabaseStats(): Promise<DatabaseStats> {
        try {
            const [turns, events, responses, members, channels, users] = await Promise.all([
                this.ctx.database.get("turns", {}),
                this.ctx.database.get("channel_events", {}),
                this.ctx.database.get("agent_responses", {}),
                this.ctx.database.get("members", {}),
                this.ctx.database.get("channel", {}),
                this.ctx.database.get("user", {}),
            ]);

            const turnCount = turns.length;
            const eventCount = events.length;
            const responseCount = responses.length;
            const memberCount = members.length;
            const channelCount = channels.length;
            const userCount = users.length;

            return {
                turns: turnCount,
                events: eventCount,
                responses: responseCount,
                members: memberCount,
                channels: channelCount,
                users: userCount,
                timestamp: new Date(),
            };
        } catch (error) {
            this.logger.error("获取数据库统计信息失败:", error);
            throw error;
        }
    }

    /**
     * 分析数据增长趋势
     */
    async analyzeGrowthTrend(days: number = 7): Promise<GrowthTrend> {
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

        try {
            // 简化的统计查询 - 获取指定时间段内的数据
            const [turns, events] = await Promise.all([this.ctx.database.get("turns", {}), this.ctx.database.get("channel_events", {})]);

            // 在内存中过滤时间范围
            const turnsInRange = turns.filter((t) => t.startTimestamp >= startDate && t.startTimestamp <= endDate);
            const eventsInRange = events.filter((e) => e.timestamp >= startDate && e.timestamp <= endDate);

            const totalTurns = turnsInRange.length;
            const totalEvents = eventsInRange.length;

            // 创建简化的统计数据
            const avgTurnsPerDay = totalTurns / Math.max(days, 1);
            const avgEventsPerDay = totalEvents / Math.max(days, 1);

            const dailyStats = [{ date: startDate.toISOString().split("T")[0], turn_count: totalTurns, active_channels: 0 }];
            const eventStats = [{ date: startDate.toISOString().split("T")[0], event_count: totalEvents }];

            return {
                period: { start: startDate, end: endDate },
                dailyTurns: dailyStats,
                dailyEvents: eventStats,
                avgTurnsPerDay,
                avgEventsPerDay,
            };
        } catch (error) {
            this.logger.error("分析数据增长趋势失败:", error);
            throw error;
        }
    }

    /**
     * 估算存储空间使用
     */
    async estimateStorageUsage(): Promise<StorageEstimate> {
        try {
            const stats = await this.getDatabaseStats();

            // 估算每种数据类型的平均大小（字节）
            const estimates = {
                turnSize: 500, // 每个回合约500字节
                eventSize: 1000, // 每个事件约1KB
                responseSize: 2000, // 每个响应约2KB
                memberSize: 300, // 每个成员约300字节
                channelSize: 200, // 每个频道约200字节
                userSize: 250, // 每个用户约250字节
            };

            const totalSize =
                stats.turns * estimates.turnSize +
                stats.events * estimates.eventSize +
                stats.responses * estimates.responseSize +
                stats.members * estimates.memberSize +
                stats.channels * estimates.channelSize +
                stats.users * estimates.userSize;

            return {
                totalSizeBytes: totalSize,
                totalSizeMB: Math.round((totalSize / 1024 / 1024) * 100) / 100,
                breakdown: {
                    turns: { count: stats.turns, sizeBytes: stats.turns * estimates.turnSize },
                    events: { count: stats.events, sizeBytes: stats.events * estimates.eventSize },
                    responses: { count: stats.responses, sizeBytes: stats.responses * estimates.responseSize },
                    members: { count: stats.members, sizeBytes: stats.members * estimates.memberSize },
                    channels: { count: stats.channels, sizeBytes: stats.channels * estimates.channelSize },
                    users: { count: stats.users, sizeBytes: stats.users * estimates.userSize },
                },
                timestamp: new Date(),
            };
        } catch (error) {
            this.logger.error("估算存储使用失败:", error);
            throw error;
        }
    }

    /**
     * 执行数据完整性检查
     */
    async performIntegrityCheck(): Promise<IntegrityCheckResult> {
        this.logger.info("开始数据完整性检查");

        const issues: IntegrityIssue[] = [];

        try {
            // 检查孤立事件 - 简化检查
            const [allEvents, allTurns] = await Promise.all([
                this.ctx.database.get("channel_events", {}),
                this.ctx.database.get("turns", {}),
            ]);

            const turnIds = new Set(allTurns.map((t) => t.id));
            const orphanedEvents = allEvents.filter((e) => !turnIds.has(e.turnId));

            if (orphanedEvents.length > 0) {
                issues.push({
                    type: "orphaned_events",
                    description: `发现 ${orphanedEvents.length} 个孤立事件`,
                    severity: "warning",
                    count: orphanedEvents.length,
                });
            }

            // 检查孤立响应
            const allResponses = await this.ctx.database.get("agent_responses", {});
            const orphanedResponses = allResponses.filter((r) => !turnIds.has(r.turnId));

            if (orphanedResponses.length > 0) {
                issues.push({
                    type: "orphaned_responses",
                    description: `发现 ${orphanedResponses.length} 个孤立响应`,
                    severity: "warning",
                    count: orphanedResponses.length,
                });
            }

            // 检查无效成员
            const [allMembers, allUsers] = await Promise.all([this.ctx.database.get("members", {}), this.ctx.database.get("user", {})]);

            const userIds = new Set(allUsers.map((u) => u.id));
            const invalidMembers = allMembers.filter((m) => !userIds.has(m.userId));

            if (invalidMembers.length > 0) {
                issues.push({
                    type: "invalid_members",
                    description: `发现 ${invalidMembers.length} 个无效成员记录`,
                    severity: "error",
                    count: invalidMembers.length,
                });
            }
        } catch (error) {
            this.logger.error("数据完整性检查失败:", error);
            issues.push({
                type: "check_error",
                description: `完整性检查过程中发生错误: ${error.message}`,
                severity: "error",
                count: 0,
            });
        }

        const result: IntegrityCheckResult = {
            timestamp: new Date(),
            totalIssues: issues.length,
            issues,
            status: issues.some((i) => i.severity === "error") ? "error" : issues.some((i) => i.severity === "warning") ? "warning" : "ok",
        };

        this.logger.info(`数据完整性检查完成，发现 ${result.totalIssues} 个问题`);
        return result;
    }
}

// 类型定义
export interface CleanupResult {
    startTime: Date;
    endTime: Date | null;
    totalProcessed: number;
    totalDeleted: number;
    details: {
        turns: CleanupStageResult;
        events: CleanupStageResult;
        responses: CleanupStageResult;
        members: CleanupStageResult;
        orphaned: CleanupStageResult;
    };
    errors: CleanupError[];
}

export interface CleanupStageResult {
    processed: number;
    deleted: number;
}

export interface CleanupError {
    stage: string;
    error: string;
    timestamp: Date;
}

export interface DatabaseStats {
    turns: number;
    events: number;
    responses: number;
    members: number;
    channels: number;
    users: number;
    timestamp: Date;
}

export interface GrowthTrend {
    period: { start: Date; end: Date };
    dailyTurns: Array<{ date: string; turn_count: number; active_channels: number }>;
    dailyEvents: Array<{ date: string; event_count: number }>;
    avgTurnsPerDay: number;
    avgEventsPerDay: number;
}

export interface StorageEstimate {
    totalSizeBytes: number;
    totalSizeMB: number;
    breakdown: {
        [key: string]: { count: number; sizeBytes: number };
    };
    timestamp: Date;
}

export interface IntegrityCheckResult {
    timestamp: Date;
    totalIssues: number;
    issues: IntegrityIssue[];
    status: "ok" | "warning" | "error";
}

export interface IntegrityIssue {
    type: string;
    description: string;
    severity: "info" | "warning" | "error";
    count: number;
}
