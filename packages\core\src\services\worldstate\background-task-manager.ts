import { $, Context, Logger } from "koishi";
import { WorldStateConfig } from "./config";
import { DataManager } from "./DataManager";

/**
 * 后台任务管理器
 * 负责定期执行成员信息更新、群组信息更新、数据清理等任务
 */
export class BackgroundTaskManager {
    private logger: Logger;
    private memberUpdateTimer?: NodeJS.Timeout;
    private channelUpdateTimer?: NodeJS.Timeout;
    private dataCleanupTimer?: NodeJS.Timeout;
    private isRunning = false;

    constructor(private ctx: Context, private config: WorldStateConfig, private dataManager: DataManager) {
        this.logger = ctx.logger("worldstate:background");
    }

    /**
     * 启动所有后台任务
     */
    start(): void {
        if (this.isRunning) {
            this.logger.warn("后台任务管理器已在运行中");
            return;
        }

        this.isRunning = true;
        this.logger.info("启动后台任务管理器");

        // 启动成员信息更新任务
        if (this.config.DataManagement.MemberUpdate.Enabled) {
            this.startMemberUpdateTask();
        }

        // 启动群组信息更新任务
        if (this.config.DataManagement.ChannelUpdate.Enabled) {
            this.startChannelUpdateTask();
        }

        // 启动数据清理任务
        if (this.config.DataCleanup.Enabled) {
            this.startDataCleanupTask();
        }
    }

    /**
     * 停止所有后台任务
     */
    stop(): void {
        if (!this.isRunning) {
            return;
        }

        this.logger.info("停止后台任务管理器");
        this.isRunning = false;

        if (this.memberUpdateTimer) {
            clearInterval(this.memberUpdateTimer);
            this.memberUpdateTimer = undefined;
        }

        if (this.channelUpdateTimer) {
            clearInterval(this.channelUpdateTimer);
            this.channelUpdateTimer = undefined;
        }

        if (this.dataCleanupTimer) {
            clearInterval(this.dataCleanupTimer);
            this.dataCleanupTimer = undefined;
        }
    }

    /**
     * 启动成员信息更新任务
     */
    private startMemberUpdateTask(): void {
        const intervalMs = this.config.DataManagement.MemberUpdate.IntervalMinutes * 60 * 1000;

        this.logger.info(`启动成员信息更新任务，间隔: ${this.config.DataManagement.MemberUpdate.IntervalMinutes} 分钟`);

        this.memberUpdateTimer = setInterval(async () => {
            try {
                await this.updateMemberInfo();
            } catch (error) {
                this.logger.error("成员信息更新任务执行失败:", error);
            }
        }, intervalMs);

        // 立即执行一次
        this.updateMemberInfo().catch((error) => {
            this.logger.error("初始成员信息更新失败:", error);
        });
    }

    /**
     * 启动群组信息更新任务
     */
    private startChannelUpdateTask(): void {
        const intervalMs = this.config.DataManagement.ChannelUpdate.IntervalMinutes * 60 * 1000;

        this.logger.info(`启动群组信息更新任务，间隔: ${this.config.DataManagement.ChannelUpdate.IntervalMinutes} 分钟`);

        this.channelUpdateTimer = setInterval(async () => {
            try {
                await this.updateChannelInfo();
            } catch (error) {
                this.logger.error("群组信息更新任务执行失败:", error);
            }
        }, intervalMs);
    }

    /**
     * 启动数据清理任务
     */
    private startDataCleanupTask(): void {
        const intervalMs = this.config.DataCleanup.IntervalHours * 60 * 60 * 1000;

        this.logger.info(`启动数据清理任务，间隔: ${this.config.DataCleanup.IntervalHours} 小时`);

        this.dataCleanupTimer = setInterval(async () => {
            try {
                await this.cleanupExpiredData();
            } catch (error) {
                this.logger.error("数据清理任务执行失败:", error);
            }
        }, intervalMs);
    }

    /**
     * 更新成员信息
     */
    private async updateMemberInfo(): Promise<void> {
        this.logger.debug("开始执行成员信息更新任务");

        const batchSize = this.config.DataManagement.MemberUpdate.BatchSize;
        let offset = 0;
        let processedCount = 0;

        while (true) {
            // 分批获取需要更新的成员
            const members = await this.ctx.database.select("members").limit(batchSize).offset(offset).execute();

            if (members.length === 0) {
                break;
            }

            // 按平台和频道分组处理
            const groupedMembers = this.groupMembersByChannel(members);

            for (const [key, memberGroup] of groupedMembers.entries()) {
                const [platform, channelId] = key.split(":");
                try {
                    await this.updateMembersInChannel(platform, channelId, memberGroup);
                    processedCount += memberGroup.length;
                } catch (error) {
                    this.logger.warn(`更新频道 ${platform}:${channelId} 的成员信息失败:`, error);
                }
            }

            offset += batchSize;
        }

        this.logger.info(`成员信息更新任务完成，处理了 ${processedCount} 个成员`);
    }

    /**
     * 更新群组信息
     */
    private async updateChannelInfo(): Promise<void> {
        this.logger.debug("开始执行群组信息更新任务");

        const channels = await this.ctx.database.get("channel", {});
        let updatedCount = 0;

        for (const channel of channels) {
            try {
                // 更新群组成员数量
                await this.dataManager.updateChannelMemberCount(channel.id, channel.platform);

                // 更新最近活跃成员数
                await this.updateRecentActiveCount(channel.platform, channel.id);

                updatedCount++;
            } catch (error) {
                this.logger.warn(`更新群组信息失败 ${channel.platform}:${channel.id}:`, error);
            }
        }

        this.logger.info(`群组信息更新任务完成，更新了 ${updatedCount} 个群组`);
    }

    /**
     * 清理过期数据
     */
    private async cleanupExpiredData(): Promise<void> {
        this.logger.debug("开始执行数据清理任务");

        const retentionDate = new Date(Date.now() - this.config.DataCleanup.RetentionDays * 24 * 60 * 60 * 1000);
        let totalCleaned = 0;

        // 清理过期的回合数据
        const expiredTurns = await this.ctx.database
            .select("turns")
            .where((row) => $.and($.lt(row.endTimestamp, retentionDate)))
            .limit(this.config.DataCleanup.BatchSize)
            .execute();

        if (expiredTurns.length > 0) {
            const turnIds = expiredTurns.map((t) => t.id);

            // 删除相关的事件和响应数据
            await this.ctx.database.remove("channel_events", { turnId: turnIds });
            await this.ctx.database.remove("agent_responses", { turnId: turnIds });
            await this.ctx.database.remove("turns", { id: turnIds });

            totalCleaned += expiredTurns.length;
        }

        this.logger.info(`数据清理任务完成，清理了 ${totalCleaned} 个过期回合及相关数据`);
    }

    /**
     * 按频道分组成员
     */
    private groupMembersByChannel(members: any[]): Map<string, any[]> {
        const grouped = new Map<string, any[]>();

        for (const member of members) {
            const key = `${member.platform}:${member.channelId}`;
            if (!grouped.has(key)) {
                grouped.set(key, []);
            }
            grouped.get(key)!.push(member);
        }

        return grouped;
    }

    /**
     * 更新指定频道的成员信息
     */
    private async updateMembersInChannel(platform: string, channelId: string, members: any[]): Promise<void> {
        const bot = this.ctx.bots[`${platform}:${this.ctx.config.selfId}`];
        if (!bot) {
            return;
        }

        for (const member of members) {
            try {
                // 尝试获取最新的用户信息
                const userInfo = await bot.getUser(member.userId);
                if (userInfo) {
                    // 更新用户基础信息
                    await this.ctx.database.upsert("user", [
                        {
                            id: member.userId,
                            name: userInfo.name,
                            avatar: userInfo.avatar,
                            updatedAt: new Date(),
                        },
                    ]);

                    // 更新成员特定信息
                    await this.ctx.database.upsert("members", [
                        {
                            userId: member.userId,
                            platform: member.platform,
                            channelId: member.channelId,
                            nick: userInfo.nick || userInfo.name,
                            lastActive: new Date(), // 如果能获取到信息，说明用户还存在
                        },
                    ]);
                }
            } catch (error) {
                // 静默处理单个用户更新失败的情况
                this.logger.debug(`更新用户 ${member.userId} 信息失败:`, error);
            }
        }
    }

    /**
     * 更新最近活跃成员数
     */
    private async updateRecentActiveCount(platform: string, channelId: string): Promise<void> {
        const recentThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天内

        const activeCount = await this.ctx.database
            .select("members")
            .where({
                platform,
                channelId,
                lastActive: { $gte: recentThreshold },
            })
            .execute()
            .then((members) => members.length);

        await this.ctx.database.upsert("channel", [
            {
                id: channelId,
                platform,
                recentActiveCount: activeCount,
            },
        ]);
    }
}
