import { Context, Logger, Session } from "koishi";
import { WorldStateConfig } from "./config";

/**
 * 消息分类器
 * 负责准确区分事件消息和用户消息，优化数据存储结构
 */
export class MessageClassifier {
    private logger: Logger;

    constructor(private ctx: Context, private config: WorldStateConfig) {
        this.logger = ctx.logger("worldstate:classifier");
    }

    /**
     * 分类消息类型
     */
    classifyMessage(session: Session): MessageClassification {
        const content = session.content;
        const messageType = this.determineMessageType(session);
        const category = this.determineCategory(session, messageType);
        const priority = this.determinePriority(session, category);

        return {
            type: messageType,
            category,
            priority,
            shouldStore: this.shouldStoreMessage(session, category),
            eventData: this.extractEventData(session, messageType),
            metadata: this.extractMetadata(session),
        };
    }

    /**
     * 确定消息基础类型
     */
    private determineMessageType(session: Session): MessageType {
        // 检查是否为系统事件
        if (this.isSystemEvent(session)) {
            return this.classifySystemEvent(session);
        }

        // 检查是否为用户消息
        if (this.isUserMessage(session)) {
            return this.classifyUserMessage(session);
        }

        // 检查是否为机器人消息
        if (this.isBotMessage(session)) {
            return "bot_message";
        }

        // 默认为未知类型
        return "unknown";
    }

    /**
     * 判断是否为系统事件
     */
    private isSystemEvent(session: Session): boolean {
        // 检查事件类型
        const eventTypes = [
            "guild-member-added",
            "guild-member-removed",
            "guild-member-updated",
            "channel-created",
            "channel-deleted",
            "channel-updated",
            "message-deleted",
            "message-updated",
        ];

        return eventTypes.includes(session.type);
    }

    /**
     * 分类系统事件类型
     */
    private classifySystemEvent(session: Session): MessageType {
        switch (session.type) {
            case "guild-member-added":
                return "user_joined";
            case "guild-member-removed":
                return "user_left";
            case "guild-member-updated":
                return "member_updated";
            case "channel-created":
            case "channel-deleted":
            case "channel-updated":
                return "channel_updated";
            case "message-deleted":
            case "message-updated":
                return "message_updated";
            default:
                return "system_notification";
        }
    }

    /**
     * 判断是否为用户消息
     */
    private isUserMessage(session: Session): boolean {
        return session.type === "message" && session.userId !== session.selfId && !this.isBotUser(session);
    }

    /**
     * 分类用户消息类型
     */
    private classifyUserMessage(session: Session): MessageType {
        const content = session.content?.trim();

        if (!content) {
            return "empty_message";
        }

        // 检查是否为命令
        if (content.startsWith("/") || content.startsWith("!")) {
            return "command_message";
        }

        // 检查是否为@消息
        if (this.isAtMessage(session)) {
            return "mention_message";
        }

        // 检查是否为回复消息
        if (session.quote) {
            return "reply_message";
        }

        // 检查消息长度
        if (content.length < this.config.MessageProcessing.Filtering.MinMessageLength) {
            return "short_message";
        }

        return "user_message";
    }

    /**
     * 判断是否为机器人消息
     */
    private isBotMessage(session: Session): boolean {
        return session.userId === session.selfId || this.isBotUser(session);
    }

    /**
     * 判断是否为机器人用户
     */
    private isBotUser(session: Session): boolean {
        // 可以通过用户名模式或其他特征识别机器人
        const botPatterns = [/bot$/i, /^bot/i, /助手$/, /机器人$/];

        const username = session.username || session.author?.name || "";
        return botPatterns.some((pattern) => pattern.test(username));
    }

    /**
     * 判断是否为@消息
     */
    private isAtMessage(session: Session): boolean {
        return session.stripped.atSelf;
    }

    /**
     * 确定消息分类
     */
    private determineCategory(session: Session, messageType: MessageType): MessageCategory {
        // 系统事件类别
        const systemEvents: MessageType[] = ["user_joined", "user_left", "member_updated", "channel_updated", "system_notification"];

        if (systemEvents.includes(messageType)) {
            return "system_event";
        }

        // 用户交互类别
        const userInteractions: MessageType[] = ["user_message", "mention_message", "reply_message", "command_message"];

        if (userInteractions.includes(messageType)) {
            return "user_interaction";
        }

        // 机器人响应类别
        if (messageType === "bot_message") {
            return "bot_response";
        }

        // 元数据类别（不重要的消息）
        const metadataTypes: MessageType[] = ["empty_message", "short_message", "message_updated", "unknown"];

        if (metadataTypes.includes(messageType)) {
            return "metadata";
        }

        return "other";
    }

    /**
     * 确定消息优先级
     */
    private determinePriority(session: Session, category: MessageCategory): MessagePriority {
        switch (category) {
            case "user_interaction":
                // @消息和回复消息优先级最高
                if (this.isAtMessage(session) || session.quote) {
                    return "high";
                }
                return "medium";

            case "system_event":
                return "medium";

            case "bot_response":
                return "low";

            case "metadata":
                return "very_low";

            default:
                return "low";
        }
    }

    /**
     * 判断是否应该存储消息
     */
    private shouldStoreMessage(session: Session, category: MessageCategory): boolean {
        // 检查是否在忽略列表中
        const ignoredTypes = this.config.MessageProcessing.Filtering.IgnoredMessageTypes;
        if (ignoredTypes.includes(session.type)) {
            return false;
        }

        // 根据分类决定是否存储
        switch (category) {
            case "user_interaction":
            case "system_event":
                return true;

            case "bot_response":
                // 只存储重要的机器人响应
                return this.isImportantBotResponse(session);

            case "metadata":
                // 通常不存储元数据类消息
                return false;

            default:
                return false;
        }
    }

    /**
     * 判断是否为重要的机器人响应
     */
    private isImportantBotResponse(session: Session): boolean {
        const content = session.content;

        // 长消息通常比较重要
        if (content.length > 100) {
            return true;
        }

        // 包含特定关键词的响应
        const importantKeywords = ["错误", "警告", "成功", "完成", "失败", "error", "warning", "success", "complete", "failed"];

        return importantKeywords.some((keyword) => content.toLowerCase().includes(keyword.toLowerCase()));
    }

    /**
     * 提取事件数据
     */
    private extractEventData(session: Session, messageType: MessageType): Record<string, any> {
        const baseData = {
            messageId: session.messageId,
            userId: session.userId,
            timestamp: new Date(session.timestamp),
        };

        switch (messageType) {
            case "user_message":
            case "mention_message":
            case "reply_message":
            case "command_message":
                return {
                    ...baseData,
                    senderId: session.userId,
                    content: session.content,
                    quote: session.quote
                        ? {
                              messageId: session.quote.messageId,
                              content: session.quote.content,
                          }
                        : null,
                };

            case "user_joined":
                return {
                    ...baseData,
                    actorId: session.operatorId || "system",
                    userId: session.userId,
                    note: this.extractJoinReason(session),
                };

            case "user_left":
                return {
                    ...baseData,
                    actorId: session.operatorId || "system",
                    userId: session.userId,
                    reason: this.extractLeaveReason(session),
                };

            case "member_updated":
                return {
                    ...baseData,
                    userId: session.userId,
                    changes: this.extractMemberChanges(session),
                };

            case "system_notification":
                return {
                    ...baseData,
                    content: session.content || "系统通知",
                    notificationType: this.extractNotificationType(session),
                };

            default:
                return baseData;
        }
    }

    /**
     * 提取加入原因
     */
    private extractJoinReason(session: Session): string | null {
        // 从session中提取加入原因
        return session.content || null;
    }

    /**
     * 提取离开原因
     */
    private extractLeaveReason(session: Session): string | null {
        // 从session中提取离开原因
        return session.content || null;
    }

    /**
     * 提取成员变更信息
     */
    private extractMemberChanges(session: Session): Record<string, any> {
        // 提取成员信息变更的具体内容
        return {
            oldValue: session.content, // 简化处理
            newValue: session.content,
            changeType: "unknown",
        };
    }

    /**
     * 提取通知类型
     */
    private extractNotificationType(session: Session): string {
        // 根据内容判断通知类型
        const content = session.content?.toLowerCase() || "";

        if (content.includes("禁言") || content.includes("mute")) {
            return "mute";
        }
        if (content.includes("解禁") || content.includes("unmute")) {
            return "unmute";
        }
        if (content.includes("管理员") || content.includes("admin")) {
            return "admin_action";
        }

        return "general";
    }

    /**
     * 提取元数据
     */
    private extractMetadata(session: Session): MessageMetadata {
        return {
            platform: session.platform,
            channelId: session.channelId,
            //@ts-ignore
            channelName: session.channel?.name,
            authorName: session.username || session.author?.name,
            authorAvatar: session.author?.avatar,
            //@ts-ignore
            isPrivate: session.channel?.type === "private",
            hasAttachments: !!(session.elements && session.elements.length > 0),
            wordCount: session.content ? session.content.length : 0,
            language: this.detectLanguage(session.content || ""),
        };
    }

    /**
     * 检测语言（简化版）
     */
    private detectLanguage(content: string): string {
        // 简化的语言检测
        const chinesePattern = /[\u4e00-\u9fff]/;
        const englishPattern = /[a-zA-Z]/;

        if (chinesePattern.test(content)) {
            return "zh";
        }
        if (englishPattern.test(content)) {
            return "en";
        }
        return "unknown";
    }

    /**
     * 批量分类消息
     */
    async classifyBatch(sessions: Session[]): Promise<MessageClassification[]> {
        return sessions.map((session) => this.classifyMessage(session));
    }

    /**
     * 获取分类统计
     */
    getClassificationStats(classifications: MessageClassification[]): ClassificationStats {
        const stats: ClassificationStats = {
            total: classifications.length,
            byCategory: new Map(),
            byType: new Map(),
            byPriority: new Map(),
            stored: 0,
            filtered: 0,
        };

        for (const classification of classifications) {
            // 统计分类
            stats.byCategory.set(classification.category, (stats.byCategory.get(classification.category) || 0) + 1);

            // 统计类型
            stats.byType.set(classification.type, (stats.byType.get(classification.type) || 0) + 1);

            // 统计优先级
            stats.byPriority.set(classification.priority, (stats.byPriority.get(classification.priority) || 0) + 1);

            // 统计存储状态
            if (classification.shouldStore) {
                stats.stored++;
            } else {
                stats.filtered++;
            }
        }

        return stats;
    }
}

// 类型定义
export type MessageType =
    | "user_message"
    | "mention_message"
    | "reply_message"
    | "command_message"
    | "user_joined"
    | "user_left"
    | "member_updated"
    | "channel_updated"
    | "system_notification"
    | "bot_message"
    | "empty_message"
    | "short_message"
    | "message_updated"
    | "unknown";

export type MessageCategory = "user_interaction" | "system_event" | "bot_response" | "metadata" | "other";

export type MessagePriority = "very_high" | "high" | "medium" | "low" | "very_low";

export interface MessageClassification {
    type: MessageType;
    category: MessageCategory;
    priority: MessagePriority;
    shouldStore: boolean;
    eventData: Record<string, any>;
    metadata: MessageMetadata;
}

export interface MessageMetadata {
    platform: string;
    channelId: string;
    channelName?: string;
    authorName?: string;
    authorAvatar?: string;
    isPrivate: boolean;
    hasAttachments: boolean;
    wordCount: number;
    language: string;
}

export interface ClassificationStats {
    total: number;
    byCategory: Map<MessageCategory, number>;
    byType: Map<MessageType, number>;
    byPriority: Map<MessagePriority, number>;
    stored: number;
    filtered: number;
}
