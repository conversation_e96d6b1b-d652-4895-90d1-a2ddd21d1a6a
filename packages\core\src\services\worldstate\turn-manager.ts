import { Context, Logger } from "koishi";
import { WorldStateConfig } from "./config";
import { TurnData } from "./model";
import { Turn } from "./interfaces";

/**
 * 回合管理器
 * 负责回合的生命周期管理、自动折叠、摘要生成等功能
 */
export class TurnManager {
    private logger: Logger;

    constructor(private ctx: Context, private config: WorldStateConfig) {
        this.logger = ctx.logger("worldstate:turn");
    }

    /**
     * 获取经过优化的回合列表
     * 自动处理过期回合的折叠和限制
     */
    async getOptimizedTurns(platform: string, channelId: string): Promise<Turn[]> {
        // 1. 获取所有回合，按时间倒序
        const allTurns = await this.ctx.database.select("turns").where({ platform, channelId }).orderBy("startTimestamp", "desc").execute();

        if (allTurns.length === 0) {
            return [];
        }

        // 2. 分类回合：活跃回合 vs 过期回合
        const activeRetentionTime = this.config.TurnManagement.Lifecycle.ActiveRetentionHours * 60 * 60 * 1000;
        const now = Date.now();

        const activeTurns: TurnData[] = [];
        const expiredTurns: TurnData[] = [];

        for (const turn of allTurns) {
            const turnAge = now - turn.startTimestamp.getTime();
            if (turnAge <= activeRetentionTime && activeTurns.length < this.config.TurnManagement.Lifecycle.MaxActiveTurns) {
                activeTurns.push(turn);
            } else {
                expiredTurns.push(turn);
            }
        }

        // 3. 处理需要自动折叠的过期回合
        if (this.config.TurnManagement.Lifecycle.AutoFoldExpired) {
            await this.autoFoldExpiredTurns(expiredTurns);
        }

        // 4. 处理需要摘要的回合
        if (this.config.TurnManagement.Summary.Enabled) {
            await this.autoSummarizeTurns([...activeTurns, ...expiredTurns]);
        }

        // 5. 构建优化后的回合对象
        const optimizedTurns: Turn[] = [];

        // 添加活跃回合（完整显示）
        for (const turnData of activeTurns) {
            const fullTurn = await this.buildFullTurn(turnData);
            optimizedTurns.push(fullTurn);
        }

        // 添加部分折叠的过期回合（保留关键信息但限制事件数量）
        const maxFoldedTurns = Math.max(0, this.config.Rendering.MaxTurnsPerChannel - activeTurns.length);
        for (let i = 0; i < Math.min(expiredTurns.length, maxFoldedTurns); i++) {
            const turnData = expiredTurns[i];
            if (turnData.status === "folded") {
                const foldedTurn = await this.buildFoldedTurn(turnData);
                optimizedTurns.push(foldedTurn);
            } else {
                const fullTurn = await this.buildFullTurn(turnData);
                optimizedTurns.push(fullTurn);
            }
        }

        return optimizedTurns;
    }

    /**
     * 自动折叠过期回合
     */
    private async autoFoldExpiredTurns(expiredTurns: TurnData[]): Promise<void> {
        const turnsToFold = expiredTurns.filter((turn) => turn.status === "full" || turn.status === "new");

        if (turnsToFold.length === 0) {
            return;
        }

        this.logger.debug(`自动折叠 ${turnsToFold.length} 个过期回合`);

        for (const turn of turnsToFold) {
            try {
                await this.foldTurn(turn.id);
            } catch (error) {
                this.logger.warn(`折叠回合 ${turn.id} 失败:`, error);
            }
        }
    }

    /**
     * 自动摘要符合条件的回合
     */
    private async autoSummarizeTurns(turns: TurnData[]): Promise<void> {
        const turnsToSummarize = await this.identifyTurnsForSummary(turns);

        if (turnsToSummarize.length === 0) {
            return;
        }

        this.logger.debug(`自动摘要 ${turnsToSummarize.length} 个回合`);

        for (const turn of turnsToSummarize) {
            try {
                await this.generateTurnSummary(turn.id);
            } catch (error) {
                this.logger.warn(`摘要回合 ${turn.id} 失败:`, error);
            }
        }
    }

    /**
     * 识别需要摘要的回合
     */
    private async identifyTurnsForSummary(turns: TurnData[]): Promise<TurnData[]> {
        const turnsToSummarize: TurnData[] = [];
        const eventCountThreshold = this.config.TurnManagement.Summary.EventCountThreshold;
        const timeThresholdMs = this.config.TurnManagement.Summary.TimeThresholdHours * 60 * 60 * 1000;
        const now = Date.now();

        for (const turn of turns) {
            // 跳过已经摘要的回合
            if (turn.status === "summarized") {
                continue;
            }

            // 检查时间条件
            const turnAge = now - turn.startTimestamp.getTime();
            const meetsTimeThreshold = turnAge >= timeThresholdMs;

            // 检查事件数量条件
            const eventCount = await this.ctx.database
                .select("channel_events")
                .where({ turnId: turn.id })
                .execute()
                .then((events) => events.length);

            const meetsEventThreshold = eventCount >= eventCountThreshold;

            if (meetsTimeThreshold || meetsEventThreshold) {
                turnsToSummarize.push(turn);
            }
        }

        return turnsToSummarize;
    }

    /**
     * 折叠回合（保留最重要的事件）
     */
    private async foldTurn(turnId: string): Promise<void> {
        // 获取回合的所有事件
        const events = await this.ctx.database.select("channel_events").where({ turnId }).orderBy("timestamp", "asc").execute();

        if (events.length <= 10) {
            // 事件较少时，仅更新状态
            await this.ctx.database.upsert("turns", [
                {
                    id: turnId,
                    status: "folded",
                },
            ]);
            return;
        }

        // 保留关键事件：开始和结束的几个事件，以及重要事件类型
        const importantEventTypes = ["user_joined", "user_left", "system_notification"];
        const keepEvents = [
            ...events.slice(0, 3), // 前3个事件
            ...events.slice(-3), // 后3个事件
            ...events.filter((event) => importantEventTypes.includes(event.type)),
        ];

        // 去重（按事件ID）
        const uniqueKeepEvents = Array.from(new Map(keepEvents.map((event) => [event.id, event])).values());

        // 删除不需要保留的事件
        const keepEventIds = uniqueKeepEvents.map((e) => e.id);
        const eventsToDelete = events.filter((e) => !keepEventIds.includes(e.id));

        if (eventsToDelete.length > 0) {
            await this.ctx.database.remove("channel_events", {
                id: eventsToDelete.map((e) => e.id),
            });
        }

        // 更新回合状态
        await this.ctx.database.upsert("turns", [
            {
                id: turnId,
                status: "folded",
            },
        ]);

        this.logger.debug(`回合 ${turnId} 已折叠，保留 ${uniqueKeepEvents.length} 个关键事件，删除 ${eventsToDelete.length} 个事件`);
    }

    /**
     * 生成回合摘要
     */
    private async generateTurnSummary(turnId: string): Promise<void> {
        // 获取回合的所有事件和响应
        const [events, responses] = await Promise.all([
            this.ctx.database.get("channel_events", { turnId }),
            this.ctx.database.get("agent_responses", { turnId }),
        ]);

        // 构建摘要内容
        const summary = this.buildTurnSummary(events, responses);

        // 更新回合状态和摘要
        await this.ctx.database.upsert("turns", [
            {
                id: turnId,
                status: "summarized",
                summary: summary,
            },
        ]);

        this.logger.debug(`回合 ${turnId} 已生成摘要`);
    }

    /**
     * 构建回合摘要
     */
    private buildTurnSummary(events: any[], responses: any[]): string {
        const summaryParts: string[] = [];

        // 统计事件类型
        const eventTypeCounts = new Map<string, number>();
        for (const event of events) {
            eventTypeCounts.set(event.type, (eventTypeCounts.get(event.type) || 0) + 1);
        }

        // 添加事件统计
        if (eventTypeCounts.size > 0) {
            const eventStats = Array.from(eventTypeCounts.entries())
                .map(([type, count]) => `${type}: ${count}`)
                .join(", ");
            summaryParts.push(`事件: ${eventStats}`);
        }

        // 添加响应统计
        if (responses.length > 0) {
            summaryParts.push(`AI响应: ${responses.length} 次`);
        }

        // 时间范围
        if (events.length > 0) {
            const startTime = new Date(Math.min(...events.map((e) => new Date(e.timestamp).getTime())));
            const endTime = new Date(Math.max(...events.map((e) => new Date(e.timestamp).getTime())));
            const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60)); // 分钟
            summaryParts.push(`持续时间: ${duration} 分钟`);
        }

        return summaryParts.join(" | ");
    }

    /**
     * 构建完整回合对象
     */
    private async buildFullTurn(turnData: TurnData): Promise<Turn> {
        const [events, responses] = await Promise.all([
            this.ctx.database
                .select("channel_events")
                .where({ turnId: turnData.id })
                .orderBy("timestamp", "asc")
                .limit(this.config.Rendering.MaxEventsPerTurn)
                .execute(),
            this.ctx.database.get("agent_responses", { turnId: turnData.id }),
        ]);

        return {
            id: turnData.id,
            status: turnData.status,
            summary: turnData.summary,
            events: await this.hydrateEvents(events),
            responses: this.hydrateResponses(responses),
        };
    }

    /**
     * 构建折叠回合对象（限制事件数量）
     */
    private async buildFoldedTurn(turnData: TurnData): Promise<Turn> {
        // 对于折叠的回合，只获取少量关键事件
        const events = await this.ctx.database
            .select("channel_events")
            .where({ turnId: turnData.id })
            .orderBy("timestamp", "asc")
            .limit(5) // 折叠回合只显示5个事件
            .execute();

        const responses = await this.ctx.database.get("agent_responses", { turnId: turnData.id });

        return {
            id: turnData.id,
            status: turnData.status,
            summary: turnData.summary || "已折叠的回合",
            events: await this.hydrateEvents(events),
            responses: this.hydrateResponses(responses),
        };
    }

    /**
     * 填充事件数据（从数据库记录转换为业务对象）
     */
    private async hydrateEvents(eventRecords: any[]): Promise<any[]> {
        // 这里可以复用 TurnRepository 中的逻辑
        // 为了简化，先返回基础转换
        return eventRecords.map((record) => ({
            id: record.id,
            type: record.type,
            timestamp: record.timestamp,
            ...record.data,
        }));
    }

    /**
     * 填充响应数据
     */
    private hydrateResponses(responseRecords: any[]): any[] {
        return responseRecords.map((record) => ({
            thoughts: record.thoughts || { obverse: "", analyze_infer: "", plan: "" },
            actions: record.actions || [],
            observations: record.observations || [],
        }));
    }

    /**
     * 手动折叠指定回合
     */
    async manualFoldTurn(turnId: string): Promise<void> {
        await this.foldTurn(turnId);
        this.logger.info(`手动折叠回合 ${turnId}`);
    }

    /**
     * 手动生成回合摘要
     */
    async manualSummarizeTurn(turnId: string): Promise<void> {
        await this.generateTurnSummary(turnId);
        this.logger.info(`手动生成回合 ${turnId} 的摘要`);
    }

    /**
     * 获取回合统计信息
     */
    async getTurnStatistics(
        platform: string,
        channelId: string
    ): Promise<{
        total: number;
        active: number;
        folded: number;
        summarized: number;
    }> {
        const turns = await this.ctx.database.get("turns", { platform, channelId });

        return {
            total: turns.length,
            active: turns.filter((t) => t.status === "new" || t.status === "full").length,
            folded: turns.filter((t) => t.status === "folded").length,
            summarized: turns.filter((t) => t.status === "summarized").length,
        };
    }
}
