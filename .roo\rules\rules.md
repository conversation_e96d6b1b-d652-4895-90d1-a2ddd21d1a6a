# Testing
- Use Bun for testing.

# Architecture Principles
- Avoid over-abstraction, over-engineering, and unnecessary complexity. Prefer straightforward, direct solutions over elaborate design patterns
- User prefers modular architecture with specialized classes
- Structure code using specialized classes with clear, single responsibilities. Each class should have a focused purpose
- User prefers complete configuration-driven architecture with no hardcoded values.
- Maintain code readability and maintainability.
- Prioritize code readability and long-term maintainability. Write self-documenting code with clear variable and function names
- Use PascalCase for configuration property names
- Do not implement configuration validation or default value handling, as Koishi framework already provides these capabilities. Rely on <PERSON>ishi's built-in configuration management features
- Use kebab-case for file naming (e.g., my-useful-class.ts) and integrate seamlessly with existing middleware architecture, ensuring coordination with reasoningMiddleware.
- User prefers unified middleware approach with reasoningMiddleware as core engine handling ReAct loops, and wants services registered uniformly in Koishi while avoiding over-abstraction.