<rules>
# Testing
- Use Bun for testing.

# Architecture Principles
- Avoid over-abstraction, over-engineering, and unnecessary complexity. Prefer straightforward, direct solutions over elaborate design patterns
- User prefers modular architecture with specialized classes
- Structure code using specialized classes with clear, single responsibilities. Each class should have a focused purpose
- User prefers complete configuration-driven architecture with no hardcoded values.
- Maintain code readability and maintainability.
- Prioritize code readability and long-term maintainability. Write self-documenting code with clear variable and function names
- Use PascalCase for configuration property names
- Do not implement configuration validation or default value handling, as Koishi framework already provides these capabilities. Rely on Koishi's built-in configuration management features
- Use kebab-case for file naming (e.g., my-useful-class.ts) and integrate seamlessly with existing middleware architecture, ensuring coordination with reasoningMiddleware.
- User prefers unified middleware approach with reasoningMiddleware as core engine handling ReAct loops, and wants services registered uniformly in Koishi while avoiding over-abstraction.
</rules>

<file_content path="packages/core/src/services/worldstate">
├── background-task-manager.ts
├── config.ts
├── data-cleanup-service.ts
├── DataManager.ts
├── index.ts
├── interfaces.ts
├── message-classifier.ts
├── model.ts
├── README.md
├── repositories/
└── turn-manager.ts

<file_content path="packages/core/src/services/worldstate/background-task-manager.ts">
  1 | import { $, Context, Logger } from "koishi";
  2 | import { WorldStateConfig } from "./config";
  3 | import { DataManager } from "./DataManager";
  4 |
  5 | /**
  6 |  * 后台任务管理器
  7 |  * 负责定期执行成员信息更新、群组信息更新、数据清理等任务
  8 |  */
  9 | export class BackgroundTaskManager {
 10 |     private logger: Logger;
 11 |     private memberUpdateTimer?: NodeJS.Timeout;
 12 |     private channelUpdateTimer?: NodeJS.Timeout;
 13 |     private dataCleanupTimer?: NodeJS.Timeout;
 14 |     private isRunning = false;
 15 |
 16 |     constructor(private ctx: Context, private config: WorldStateConfig, private dataManager: DataManager) {
 17 |         this.logger = ctx.logger("worldstate:background");
 18 |     }
 19 |
 20 |     /**
 21 |      * 启动所有后台任务
 22 |      */
 23 |     start(): void {
 24 |         if (this.isRunning) {
 25 |             this.logger.warn("后台任务管理器已在运行中");
 26 |             return;
 27 |         }
 28 |
 29 |         this.isRunning = true;
 30 |         this.logger.info("启动后台任务管理器");
 31 |
 32 |         // 启动成员信息更新任务
 33 |         if (this.config.DataManagement.MemberUpdate.Enabled) {
 34 |             this.startMemberUpdateTask();
 35 |         }
 36 |
 37 |         // 启动群组信息更新任务
 38 |         if (this.config.DataManagement.ChannelUpdate.Enabled) {
 39 |             this.startChannelUpdateTask();
 40 |         }
 41 |
 42 |         // 启动数据清理任务
 43 |         if (this.config.DataCleanup.Enabled) {
 44 |             this.startDataCleanupTask();
 45 |         }
 46 |     }
 47 |
 48 |     /**
 49 |      * 停止所有后台任务
 50 |      */
 51 |     stop(): void {
 52 |         if (!this.isRunning) {
 53 |             return;
 54 |         }
 55 |
 56 |         this.logger.info("停止后台任务管理器");
 57 |         this.isRunning = false;
 58 |
 59 |         if (this.memberUpdateTimer) {
 60 |             clearInterval(this.memberUpdateTimer);
 61 |             this.memberUpdateTimer = undefined;
 62 |         }
 63 |
 64 |         if (this.channelUpdateTimer) {
 65 |             clearInterval(this.channelUpdateTimer);
 66 |             this.channelUpdateTimer = undefined;
 67 |         }
 68 |
 69 |         if (this.dataCleanupTimer) {
 70 |             clearInterval(this.dataCleanupTimer);
 71 |             this.dataCleanupTimer = undefined;
 72 |         }
 73 |     }
 74 |
 75 |     /**
 76 |      * 启动成员信息更新任务
 77 |      */
 78 |     private startMemberUpdateTask(): void {
 79 |         const intervalMs = this.config.DataManagement.MemberUpdate.IntervalMinutes * 60 * 1000;
 80 |
 81 |         this.logger.info(`启动成员信息更新任务，间隔: ${this.config.DataManagement.MemberUpdate.IntervalMinutes} 分钟`);
 82 |
 83 |         this.memberUpdateTimer = setInterval(async () => {
 84 |             try {
 85 |                 await this.updateMemberInfo();
 86 |             } catch (error) {
 87 |                 this.logger.error("成员信息更新任务执行失败:", error);
 88 |             }
 89 |         }, intervalMs);
 90 |
 91 |         // 立即执行一次
 92 |         this.updateMemberInfo().catch((error) => {
 93 |             this.logger.error("初始成员信息更新失败:", error);
 94 |         });
 95 |     }
 96 |
 97 |     /**
 98 |      * 启动群组信息更新任务
 99 |      */
100 |     private startChannelUpdateTask(): void {
101 |         const intervalMs = this.config.DataManagement.ChannelUpdate.IntervalMinutes * 60 * 1000;
102 |
103 |         this.logger.info(`启动群组信息更新任务，间隔: ${this.config.DataManagement.ChannelUpdate.IntervalMinutes} 分钟`);
104 |
105 |         this.channelUpdateTimer = setInterval(async () => {
106 |             try {
107 |                 await this.updateChannelInfo();
108 |             } catch (error) {
109 |                 this.logger.error("群组信息更新任务执行失败:", error);
110 |             }
111 |         }, intervalMs);
112 |     }
113 |
114 |     /**
115 |      * 启动数据清理任务
116 |      */
117 |     private startDataCleanupTask(): void {
118 |         const intervalMs = this.config.DataCleanup.IntervalHours * 60 * 60 * 1000;
119 |
120 |         this.logger.info(`启动数据清理任务，间隔: ${this.config.DataCleanup.IntervalHours} 小时`);
121 |
122 |         this.dataCleanupTimer = setInterval(async () => {
123 |             try {
124 |                 await this.cleanupExpiredData();
125 |             } catch (error) {
126 |                 this.logger.error("数据清理任务执行失败:", error);
127 |             }
128 |         }, intervalMs);
129 |     }
130 |
131 |     /**
132 |      * 更新成员信息
133 |      */
134 |     private async updateMemberInfo(): Promise<void> {
135 |         this.logger.debug("开始执行成员信息更新任务");
136 |
137 |         const batchSize = this.config.DataManagement.MemberUpdate.BatchSize;
138 |         let offset = 0;
139 |         let processedCount = 0;
140 |
141 |         while (true) {
142 |             // 分批获取需要更新的成员
143 |             const members = await this.ctx.database.select("members").limit(batchSize).offset(offset).execute();
144 |
145 |             if (members.length === 0) {
146 |                 break;
147 |             }
148 |
149 |             // 按平台和频道分组处理
150 |             const groupedMembers = this.groupMembersByChannel(members);
151 |
152 |             for (const [key, memberGroup] of groupedMembers.entries()) {
153 |                 const [platform, channelId] = key.split(":");
154 |                 try {
155 |                     await this.updateMembersInChannel(platform, channelId, memberGroup);
156 |                     processedCount += memberGroup.length;
157 |                 } catch (error) {
158 |                     this.logger.warn(`更新频道 ${platform}:${channelId} 的成员信息失败:`, error);
159 |                 }
160 |             }
161 |
162 |             offset += batchSize;
163 |         }
164 |
165 |         this.logger.info(`成员信息更新任务完成，处理了 ${processedCount} 个成员`);
166 |     }
167 |
168 |     /**
169 |      * 更新群组信息
170 |      */
171 |     private async updateChannelInfo(): Promise<void> {
172 |         this.logger.debug("开始执行群组信息更新任务");
173 |
174 |         const channels = await this.ctx.database.get("channel", {});
175 |         let updatedCount = 0;
176 |
177 |         for (const channel of channels) {
178 |             try {
179 |                 // 更新群组成员数量
180 |                 await this.dataManager.updateChannelMemberCount(channel.id, channel.platform);
181 |
182 |                 // 更新最近活跃成员数
183 |                 await this.updateRecentActiveCount(channel.platform, channel.id);
184 |
185 |                 updatedCount++;
186 |             } catch (error) {
187 |                 this.logger.warn(`更新群组信息失败 ${channel.platform}:${channel.id}:`, error);
188 |             }
189 |         }
190 |
191 |         this.logger.info(`群组信息更新任务完成，更新了 ${updatedCount} 个群组`);
192 |     }
193 |
194 |     /**
195 |      * 清理过期数据
196 |      */
197 |     private async cleanupExpiredData(): Promise<void> {
198 |         this.logger.debug("开始执行数据清理任务");
199 |
200 |         const retentionDate = new Date(Date.now() - this.config.DataCleanup.RetentionDays * 24 * 60 * 60 * 1000);
201 |         let totalCleaned = 0;
202 |
203 |         // 清理过期的回合数据
204 |         const expiredTurns = await this.ctx.database
205 |             .select("turns")
206 |             .where((row) => $.and($.lt(row.endTimestamp, retentionDate)))
207 |             .limit(this.config.DataCleanup.BatchSize)
208 |             .execute();
209 |
210 |         if (expiredTurns.length > 0) {
211 |             const turnIds = expiredTurns.map((t) => t.id);
212 |
213 |             // 删除相关的事件和响应数据
214 |             await this.ctx.database.remove("channel_events", { turnId: turnIds });
215 |             await this.ctx.database.remove("agent_responses", { turnId: turnIds });
216 |             await this.ctx.database.remove("turns", { id: turnIds });
217 |
218 |             totalCleaned += expiredTurns.length;
219 |         }
220 |
221 |         this.logger.info(`数据清理任务完成，清理了 ${totalCleaned} 个过期回合及相关数据`);
222 |     }
223 |
224 |     /**
225 |      * 按频道分组成员
226 |      */
227 |     private groupMembersByChannel(members: any[]): Map<string, any[]> {
228 |         const grouped = new Map<string, any[]>();
229 |
230 |         for (const member of members) {
231 |             const key = `${member.platform}:${member.channelId}`;
232 |             if (!grouped.has(key)) {
233 |                 grouped.set(key, []);
234 |             }
235 |             grouped.get(key)!.push(member);
236 |         }
237 |
238 |         return grouped;
239 |     }
240 |
241 |     /**
242 |      * 更新指定频道的成员信息
243 |      */
244 |     private async updateMembersInChannel(platform: string, channelId: string, members: any[]): Promise<void> {
245 |         const bot = this.ctx.bots[`${platform}:${this.ctx.config.selfId}`];
246 |         if (!bot) {
247 |             return;
248 |         }
249 |
250 |         for (const member of members) {
251 |             try {
252 |                 // 尝试获取最新的用户信息
253 |                 const userInfo = await bot.getUser(member.userId);
254 |                 if (userInfo) {
255 |                     // 更新用户基础信息
256 |                     await this.ctx.database.upsert("user", [
257 |                         {
258 |                             id: member.userId,
259 |                             name: userInfo.name,
260 |                             avatar: userInfo.avatar,
261 |                             updatedAt: new Date(),
262 |                         },
263 |                     ]);
264 |
265 |                     // 更新成员特定信息
266 |                     await this.ctx.database.upsert("members", [
267 |                         {
268 |                             userId: member.userId,
269 |                             platform: member.platform,
270 |                             channelId: member.channelId,
271 |                             nick: userInfo.nick || userInfo.name,
272 |                             lastActive: new Date(), // 如果能获取到信息，说明用户还存在
273 |                         },
274 |                     ]);
275 |                 }
276 |             } catch (error) {
277 |                 // 静默处理单个用户更新失败的情况
278 |                 this.logger.debug(`更新用户 ${member.userId} 信息失败:`, error);
279 |             }
280 |         }
281 |     }
282 |
283 |     /**
284 |      * 更新最近活跃成员数
285 |      */
286 |     private async updateRecentActiveCount(platform: string, channelId: string): Promise<void> {
287 |         const recentThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天内
288 |
289 |         const activeCount = await this.ctx.database
290 |             .select("members")
291 |             .where({
292 |                 platform,
293 |                 channelId,
294 |                 lastActive: { $gte: recentThreshold },
295 |             })
296 |             .execute()
297 |             .then((members) => members.length);
298 |
299 |         await this.ctx.database.upsert("channel", [
300 |             {
301 |                 id: channelId,
302 |                 platform,
303 |                 recentActiveCount: activeCount,
304 |             },
305 |         ]);
306 |     }
307 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/config.ts">
  1 | import { Schema } from "koishi";
  2 |
  3 | export interface WorldStateConfig {
  4 |     /**
  5 |      * 数据管理配置
  6 |      */
  7 |     DataManagement: {
  8 |         /**
  9 |          * 成员信息更新配置
 10 |          */
 11 |         MemberUpdate: {
 12 |             /**
 13 |              * 是否启用定期成员信息更新
 14 |              */
 15 |             Enabled: boolean;
 16 |             /**
 17 |              * 更新间隔（分钟）
 18 |              */
 19 |             IntervalMinutes: number;
 20 |             /**
 21 |              * 批量更新大小
 22 |              */
 23 |             BatchSize: number;
 24 |         };
 25 |
 26 |         /**
 27 |          * 群组信息更新配置
 28 |          */
 29 |         ChannelUpdate: {
 30 |             /**
 31 |              * 是否启用定期群组信息更新
 32 |              */
 33 |             Enabled: boolean;
 34 |             /**
 35 |              * 更新间隔（分钟）
 36 |              */
 37 |             IntervalMinutes: number;
 38 |         };
 39 |     };
 40 |
 41 |     /**
 42 |      * 回合管理配置
 43 |      */
 44 |     TurnManagement: {
 45 |         /**
 46 |          * 回合生命周期配置
 47 |          */
 48 |         Lifecycle: {
 49 |             /**
 50 |              * 活跃回合保留时间（小时）
 51 |              */
 52 |             ActiveRetentionHours: number;
 53 |             /**
 54 |              * 最大保留回合数
 55 |              */
 56 |             MaxActiveTurns: number;
 57 |             /**
 58 |              * 自动折叠过期回合
 59 |              */
 60 |             AutoFoldExpired: boolean;
 61 |         };
 62 |
 63 |         /**
 64 |          * 回合摘要配置
 65 |          */
 66 |         Summary: {
 67 |             /**
 68 |              * 是否启用自动摘要
 69 |              */
 70 |             Enabled: boolean;
 71 |             /**
 72 |              * 触发摘要的事件数阈值
 73 |              */
 74 |             EventCountThreshold: number;
 75 |             /**
 76 |              * 触发摘要的时间阈值（小时）
 77 |              */
 78 |             TimeThresholdHours: number;
 79 |         };
 80 |     };
 81 |
 82 |     /**
 83 |      * 消息处理配置
 84 |      */
 85 |     MessageProcessing: {
 86 |         /**
 87 |          * 消息分类配置
 88 |          */
 89 |         Classification: {
 90 |             /**
 91 |              * 用户消息关键词
 92 |              */
 93 |             UserMessageKeywords: string[];
 94 |             /**
 95 |              * 系统事件关键词
 96 |              */
 97 |             SystemEventKeywords: string[];
 98 |         };
 99 |
100 |         /**
101 |          * 消息过滤配置
102 |          */
103 |         Filtering: {
104 |             /**
105 |              * 最小消息长度
106 |              */
107 |             MinMessageLength: number;
108 |             /**
109 |              * 忽略的消息类型
110 |              */
111 |             IgnoredMessageTypes: string[];
112 |         };
113 |     };
114 |
115 |     /**
116 |      * 数据清理配置
117 |      */
118 |     DataCleanup: {
119 |         /**
120 |          * 是否启用定期清理
121 |          */
122 |         Enabled: boolean;
123 |         /**
124 |          * 清理间隔（小时）
125 |          */
126 |         IntervalHours: number;
127 |         /**
128 |          * 数据保留期限（天）
129 |          */
130 |         RetentionDays: number;
131 |         /**
132 |          * 清理批量大小
133 |          */
134 |         BatchSize: number;
135 |     };
136 |
137 |     /**
138 |      * 渲染配置
139 |      */
140 |     Rendering: {
141 |         /**
142 |          * 活跃频道显示限制
143 |          */
144 |         ActiveChannelLimit: number;
145 |         /**
146 |          * 每个频道显示的最大回合数
147 |          */
148 |         MaxTurnsPerChannel: number;
149 |         /**
150 |          * 每个回合显示的最大事件数
151 |          */
152 |         MaxEventsPerTurn: number;
153 |     };
154 | }
155 |
156 | export const WorldStateConfigSchema: Schema<WorldStateConfig> = Schema.object({
157 |     DataManagement: Schema.object({
158 |         MemberUpdate: Schema.object({
159 |             Enabled: Schema.boolean().default(true).description("是否启用定期成员信息更新"),
160 |             IntervalMinutes: Schema.number().min(5).max(1440).default(60).description("更新间隔（分钟）"),
161 |             BatchSize: Schema.number().min(10).max(1000).default(100).description("批量更新大小"),
162 |         }).description("成员信息更新配置"),
163 |
164 |         ChannelUpdate: Schema.object({
165 |             Enabled: Schema.boolean().default(true).description("是否启用定期群组信息更新"),
166 |             IntervalMinutes: Schema.number().min(10).max(1440).default(120).description("更新间隔（分钟）"),
167 |         }).description("群组信息更新配置"),
168 |     }).description("数据管理配置"),
169 |
170 |     TurnManagement: Schema.object({
171 |         Lifecycle: Schema.object({
172 |             ActiveRetentionHours: Schema.number().min(1).max(168).default(24).description("活跃回合保留时间（小时）"),
173 |             MaxActiveTurns: Schema.number().min(5).max(100).default(20).description("最大保留回合数"),
174 |             AutoFoldExpired: Schema.boolean().default(true).description("自动折叠过期回合"),
175 |         }).description("回合生命周期配置"),
176 |
177 |         Summary: Schema.object({
178 |             Enabled: Schema.boolean().default(true).description("是否启用自动摘要"),
179 |             EventCountThreshold: Schema.number().min(10).max(500).default(50).description("触发摘要的事件数阈值"),
180 |             TimeThresholdHours: Schema.number().min(1).max(72).default(12).description("触发摘要的时间阈值（小时）"),
181 |         }).description("回合摘要配置"),
182 |     }).description("回合管理配置"),
183 |
184 |     MessageProcessing: Schema.object({
185 |         Classification: Schema.object({
186 |             UserMessageKeywords: Schema.array(String).default(["message_sent", "message", "user_message"]).description("用户消息关键词"),
187 |             SystemEventKeywords: Schema.array(String)
188 |                 .default(["user_joined", "user_left", "system_notification", "member_updated"])
189 |                 .description("系统事件关键词"),
190 |         }).description("消息分类配置"),
191 |
192 |         Filtering: Schema.object({
193 |             MinMessageLength: Schema.number().min(0).max(100).default(1).description("最小消息长度"),
194 |             IgnoredMessageTypes: Schema.array(String).default(["heartbeat", "typing", "presence"]).description("忽略的消息类型"),
195 |         }).description("消息过滤配置"),
196 |     }).description("消息处理配置"),
197 |
198 |     DataCleanup: Schema.object({
199 |         Enabled: Schema.boolean().default(true).description("是否启用定期清理"),
200 |         IntervalHours: Schema.number().min(1).max(168).default(24).description("清理间隔（小时）"),
201 |         RetentionDays: Schema.number().min(1).max(365).default(30).description("数据保留期限（天）"),
202 |         BatchSize: Schema.number().min(100).max(10000).default(1000).description("清理批量大小"),
203 |     }).description("数据清理配置"),
204 |
205 |     Rendering: Schema.object({
206 |         ActiveChannelLimit: Schema.number().min(1).max(50).default(10).description("活跃频道显示限制"),
207 |         MaxTurnsPerChannel: Schema.number().min(1).max(100).default(10).description("每个频道显示的最大回合数"),
208 |         MaxEventsPerTurn: Schema.number().min(5).max(500).default(50).description("每个回合显示的最大事件数"),
209 |     }).description("渲染配置"),
210 | });

</file_content>

<file_content path="packages/core/src/services/worldstate/data-cleanup-service.ts">
  1 | import { $, Context, Logger } from "koishi";
  2 | import { WorldStateConfig } from "./config";
  3 |
  4 | /**
  5 |  * 数据清理服务
  6 |  * 负责管理数据生命周期，清理过期数据，优化存储空间
  7 |  */
  8 | export class DataCleanupService {
  9 |     private logger: Logger;
 10 |
 11 |     constructor(private ctx: Context, private config: WorldStateConfig) {
 12 |         this.logger = ctx.logger("worldstate:cleanup");
 13 |     }
 14 |
 15 |     /**
 16 |      * 执行完整的数据清理流程
 17 |      */
 18 |     async performFullCleanup(): Promise<CleanupResult> {
 19 |         this.logger.info("开始执行完整数据清理");
 20 |         const startTime = Date.now();
 21 |
 22 |         const result: CleanupResult = {
 23 |             startTime: new Date(),
 24 |             endTime: null,
 25 |             totalProcessed: 0,
 26 |             totalDeleted: 0,
 27 |             details: {
 28 |                 turns: { processed: 0, deleted: 0 },
 29 |                 events: { processed: 0, deleted: 0 },
 30 |                 responses: { processed: 0, deleted: 0 },
 31 |                 members: { processed: 0, deleted: 0 },
 32 |                 orphaned: { processed: 0, deleted: 0 },
 33 |             },
 34 |             errors: [],
 35 |         };
 36 |
 37 |         try {
 38 |             // 1. 清理过期回合
 39 |             const turnResult = await this.cleanupExpiredTurns();
 40 |             result.details.turns = turnResult;
 41 |             result.totalProcessed += turnResult.processed;
 42 |             result.totalDeleted += turnResult.deleted;
 43 |
 44 |             // 2. 清理孤立事件
 45 |             const eventResult = await this.cleanupOrphanedEvents();
 46 |             result.details.events = eventResult;
 47 |             result.totalProcessed += eventResult.processed;
 48 |             result.totalDeleted += eventResult.deleted;
 49 |
 50 |             // 3. 清理孤立响应
 51 |             const responseResult = await this.cleanupOrphanedResponses();
 52 |             result.details.responses = responseResult;
 53 |             result.totalProcessed += responseResult.processed;
 54 |             result.totalDeleted += responseResult.deleted;
 55 |
 56 |             // 4. 清理无效成员
 57 |             const memberResult = await this.cleanupInvalidMembers();
 58 |             result.details.members = memberResult;
 59 |             result.totalProcessed += memberResult.processed;
 60 |             result.totalDeleted += memberResult.deleted;
 61 |
 62 |             // 5. 清理其他孤立数据
 63 |             const orphanedResult = await this.cleanupOrphanedData();
 64 |             result.details.orphaned = orphanedResult;
 65 |             result.totalProcessed += orphanedResult.processed;
 66 |             result.totalDeleted += orphanedResult.deleted;
 67 |         } catch (error) {
 68 |             this.logger.error("数据清理过程中发生错误:", error);
 69 |             result.errors.push({
 70 |                 stage: "general",
 71 |                 error: error.message,
 72 |                 timestamp: new Date(),
 73 |             });
 74 |         }
 75 |
 76 |         result.endTime = new Date();
 77 |         const duration = Date.now() - startTime;
 78 |
 79 |         this.logger.info(`数据清理完成，耗时 ${duration}ms，处理 ${result.totalProcessed} 条记录，删除 ${result.totalDeleted} 条记录`);
 80 |
 81 |         return result;
 82 |     }
 83 |
 84 |     /**
 85 |      * 清理过期回合
 86 |      */
 87 |     async cleanupExpiredTurns(): Promise<CleanupStageResult> {
 88 |         this.logger.debug("开始清理过期回合");
 89 |
 90 |         const retentionDate = new Date(Date.now() - this.config.DataCleanup.RetentionDays * 24 * 60 * 60 * 1000);
 91 |
 92 |         let processed = 0;
 93 |         let deleted = 0;
 94 |         const batchSize = this.config.DataCleanup.BatchSize;
 95 |
 96 |         while (true) {
 97 |             try {
 98 |                 // 分批获取过期回合
 99 |                 const expiredTurns = await this.ctx.database
100 |                     .select("turns")
101 |                     .where((row) => $.and($.lt(row.endTimestamp, retentionDate)))
102 |                     .limit(batchSize)
103 |                     .execute();
104 |
105 |                 if (expiredTurns.length === 0) {
106 |                     break;
107 |                 }
108 |
109 |                 processed += expiredTurns.length;
110 |                 const turnIds = expiredTurns.map((t) => t.id);
111 |
112 |                 // 删除相关的事件数据
113 |                 await this.ctx.database.remove("channel_events", { turnId: turnIds });
114 |
115 |                 // 删除相关的响应数据
116 |                 await this.ctx.database.remove("agent_responses", { turnId: turnIds });
117 |
118 |                 // 删除回合本身
119 |                 const deleteResult = await this.ctx.database.remove("turns", { id: turnIds });
120 |                 deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
121 |
122 |                 this.logger.debug(`批次清理完成：处理 ${expiredTurns.length} 个回合，删除 ${deleteResult.removed} 个`);
123 |             } catch (error) {
124 |                 this.logger.error("清理过期回合时发生错误:", error);
125 |                 break;
126 |             }
127 |         }
128 |
129 |         this.logger.info(`过期回合清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
130 |         return { processed, deleted };
131 |     }
132 |
133 |     /**
134 |      * 清理孤立事件（没有对应回合的事件）
135 |      */
136 |     async cleanupOrphanedEvents(): Promise<CleanupStageResult> {
137 |         this.logger.debug("开始清理孤立事件");
138 |
139 |         let processed = 0;
140 |         let deleted = 0;
141 |         const batchSize = this.config.DataCleanup.BatchSize;
142 |
143 |         while (true) {
144 |             try {
145 |                 // 简化查找孤立事件 - 分批获取事件，然后检查对应的回合是否存在
146 |                 const events = await this.ctx.database.select("channel_events").limit(batchSize).offset(processed).execute();
147 |
148 |                 if (events.length === 0) {
149 |                     break;
150 |                 }
151 |
152 |                 // 检查这些事件对应的回合是否存在
153 |                 const turnIds = events.map((e) => e.turnId);
154 |                 const existingTurns = await this.ctx.database.get("turns", { id: turnIds });
155 |                 const existingTurnIds = new Set(existingTurns.map((t) => t.id));
156 |
157 |                 // 找出孤立的事件
158 |                 const orphanedEvents = events.filter((e) => !existingTurnIds.has(e.turnId));
159 |
160 |                 if (orphanedEvents.length > 0) {
161 |                     const eventIds = orphanedEvents.map((e) => e.id);
162 |                     const deleteResult = await this.ctx.database.remove("channel_events", { id: eventIds });
163 |                     deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
164 |                 }
165 |
166 |                 processed += events.length;
167 |
168 |                 this.logger.debug(`批次清理孤立事件完成：处理 ${events.length} 个，删除 ${orphanedEvents.length} 个`);
169 |             } catch (error) {
170 |                 this.logger.error("清理孤立事件时发生错误:", error);
171 |                 break;
172 |             }
173 |         }
174 |
175 |         this.logger.info(`孤立事件清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
176 |         return { processed, deleted };
177 |     }
178 |
179 |     /**
180 |      * 清理孤立响应（没有对应回合的响应）
181 |      */
182 |     async cleanupOrphanedResponses(): Promise<CleanupStageResult> {
183 |         this.logger.debug("开始清理孤立响应");
184 |
185 |         let processed = 0;
186 |         let deleted = 0;
187 |         const batchSize = this.config.DataCleanup.BatchSize;
188 |
189 |         while (true) {
190 |             try {
191 |                 // 简化查找孤立响应
192 |                 const responses = await this.ctx.database.select("agent_responses").limit(batchSize).offset(processed).execute();
193 |
194 |                 if (responses.length === 0) {
195 |                     break;
196 |                 }
197 |
198 |                 // 检查这些响应对应的回合是否存在
199 |                 const turnIds = responses.map((r) => r.turnId);
200 |                 const existingTurns = await this.ctx.database.get("turns", { id: turnIds });
201 |                 const existingTurnIds = new Set(existingTurns.map((t) => t.id));
202 |
203 |                 // 找出孤立的响应
204 |                 const orphanedResponses = responses.filter((r) => !existingTurnIds.has(r.turnId));
205 |
206 |                 if (orphanedResponses.length > 0) {
207 |                     const responseIds = orphanedResponses.map((r) => r.id);
208 |                     const deleteResult = await this.ctx.database.remove("agent_responses", { id: responseIds });
209 |                     deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
210 |                 }
211 |
212 |                 processed += responses.length;
213 |
214 |                 this.logger.debug(`批次清理孤立响应完成：处理 ${responses.length} 个，删除 ${orphanedResponses.length} 个`);
215 |             } catch (error) {
216 |                 this.logger.error("清理孤立响应时发生错误:", error);
217 |                 break;
218 |             }
219 |         }
220 |
221 |         this.logger.info(`孤立响应清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
222 |         return { processed, deleted };
223 |     }
224 |
225 |     /**
226 |      * 清理无效成员（对应用户或频道不存在的成员记录）
227 |      */
228 |     async cleanupInvalidMembers(): Promise<CleanupStageResult> {
229 |         this.logger.debug("开始清理无效成员");
230 |
231 |         let processed = 0;
232 |         let deleted = 0;
233 |         const batchSize = this.config.DataCleanup.BatchSize;
234 |
235 |         while (true) {
236 |             try {
237 |                 // 简化查找无效成员
238 |                 const members = await this.ctx.database.select("members").limit(batchSize).offset(processed).execute();
239 |
240 |                 if (members.length === 0) {
241 |                     break;
242 |                 }
243 |
244 |                 // 检查这些成员对应的用户是否存在
245 |                 const userIds = members.map((m) => m.userId);
246 |                 const existingUsers = await this.ctx.database.get("user", { id: userIds });
247 |                 const existingUserIds = new Set(existingUsers.map((u) => u.id));
248 |
249 |                 // 找出无效的成员
250 |                 const invalidMembers = members.filter((m) => !existingUserIds.has(m.userId));
251 |
252 |                 let batchDeleted = 0;
253 |                 for (const member of invalidMembers) {
254 |                     const deleteResult = await this.ctx.database.remove("members", {
255 |                         userId: member.userId,
256 |                         platform: member.platform,
257 |                         channelId: member.channelId,
258 |                     });
259 |                     batchDeleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
260 |                 }
261 |
262 |                 processed += members.length;
263 |                 deleted += batchDeleted;
264 |
265 |                 this.logger.debug(`批次清理无效成员完成：处理 ${members.length} 个，删除 ${batchDeleted} 个`);
266 |             } catch (error) {
267 |                 this.logger.error("清理无效成员时发生错误:", error);
268 |                 break;
269 |             }
270 |         }
271 |
272 |         this.logger.info(`无效成员清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
273 |         return { processed, deleted };
274 |     }
275 |
276 |     /**
277 |      * 清理其他孤立数据
278 |      */
279 |     async cleanupOrphanedData(): Promise<CleanupStageResult> {
280 |         this.logger.debug("开始清理其他孤立数据");
281 |
282 |         let processed = 0;
283 |         let deleted = 0;
284 |
285 |         try {
286 |             // 清理无效的频道记录（没有任何成员或回合的频道）
287 |             const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
288 |             const emptyChannels = await this.ctx.database
289 |                 .select("channel")
290 |                 .where((row) => $.lt(row.lastActivityAt, thirtyDaysAgo))
291 |                 .execute();
292 |
293 |             // 过滤出真正没有成员和回合的频道
294 |             const filteredEmptyChannels = [];
295 |             for (const channel of emptyChannels) {
296 |                 const [members, turns] = await Promise.all([
297 |                     this.ctx.database.get("members", { channelId: channel.id, platform: channel.platform }),
298 |                     this.ctx.database.get("turns", { channelId: channel.id, platform: channel.platform }),
299 |                 ]);
300 |
301 |                 if (members.length === 0 && turns.length === 0) {
302 |                     filteredEmptyChannels.push(channel);
303 |                 }
304 |             }
305 |
306 |             if (filteredEmptyChannels.length > 0) {
307 |                 processed += filteredEmptyChannels.length;
308 |
309 |                 for (const channel of filteredEmptyChannels) {
310 |                     const deleteResult = await this.ctx.database.remove("channel", {
311 |                         id: channel.id,
312 |                         platform: channel.platform,
313 |                     });
314 |                     deleted += Array.isArray(deleteResult) ? deleteResult.length : deleteResult.matched || 0;
315 |                 }
316 |
317 |                 this.logger.debug(`清理空闲频道：处理 ${filteredEmptyChannels.length} 个，删除 ${deleted} 个`);
318 |             }
319 |         } catch (error) {
320 |             this.logger.error("清理孤立数据时发生错误:", error);
321 |         }
322 |
323 |         this.logger.info(`孤立数据清理完成：处理 ${processed} 个，删除 ${deleted} 个`);
324 |         return { processed, deleted };
325 |     }
326 |
327 |     /**
328 |      * 获取数据库统计信息
329 |      */
330 |     async getDatabaseStats(): Promise<DatabaseStats> {
331 |         try {
332 |             const [turns, events, responses, members, channels, users] = await Promise.all([
333 |                 this.ctx.database.get("turns", {}),
334 |                 this.ctx.database.get("channel_events", {}),
335 |                 this.ctx.database.get("agent_responses", {}),
336 |                 this.ctx.database.get("members", {}),
337 |                 this.ctx.database.get("channel", {}),
338 |                 this.ctx.database.get("user", {}),
339 |             ]);
340 |
341 |             const turnCount = turns.length;
342 |             const eventCount = events.length;
343 |             const responseCount = responses.length;
344 |             const memberCount = members.length;
345 |             const channelCount = channels.length;
346 |             const userCount = users.length;
347 |
348 |             return {
349 |                 turns: turnCount,
350 |                 events: eventCount,
351 |                 responses: responseCount,
352 |                 members: memberCount,
353 |                 channels: channelCount,
354 |                 users: userCount,
355 |                 timestamp: new Date(),
356 |             };
357 |         } catch (error) {
358 |             this.logger.error("获取数据库统计信息失败:", error);
359 |             throw error;
360 |         }
361 |     }
362 |
363 |     /**
364 |      * 分析数据增长趋势
365 |      */
366 |     async analyzeGrowthTrend(days: number = 7): Promise<GrowthTrend> {
367 |         const endDate = new Date();
368 |         const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
369 |
370 |         try {
371 |             // 简化的统计查询 - 获取指定时间段内的数据
372 |             const [turns, events] = await Promise.all([this.ctx.database.get("turns", {}), this.ctx.database.get("channel_events", {})]);
373 |
374 |             // 在内存中过滤时间范围
375 |             const turnsInRange = turns.filter((t) => t.startTimestamp >= startDate && t.startTimestamp <= endDate);
376 |             const eventsInRange = events.filter((e) => e.timestamp >= startDate && e.timestamp <= endDate);
377 |
378 |             const totalTurns = turnsInRange.length;
379 |             const totalEvents = eventsInRange.length;
380 |
381 |             // 创建简化的统计数据
382 |             const avgTurnsPerDay = totalTurns / Math.max(days, 1);
383 |             const avgEventsPerDay = totalEvents / Math.max(days, 1);
384 |
385 |             const dailyStats = [{ date: startDate.toISOString().split("T")[0], turn_count: totalTurns, active_channels: 0 }];
386 |             const eventStats = [{ date: startDate.toISOString().split("T")[0], event_count: totalEvents }];
387 |
388 |             return {
389 |                 period: { start: startDate, end: endDate },
390 |                 dailyTurns: dailyStats,
391 |                 dailyEvents: eventStats,
392 |                 avgTurnsPerDay,
393 |                 avgEventsPerDay,
394 |             };
395 |         } catch (error) {
396 |             this.logger.error("分析数据增长趋势失败:", error);
397 |             throw error;
398 |         }
399 |     }
400 |
401 |     /**
402 |      * 估算存储空间使用
403 |      */
404 |     async estimateStorageUsage(): Promise<StorageEstimate> {
405 |         try {
406 |             const stats = await this.getDatabaseStats();
407 |
408 |             // 估算每种数据类型的平均大小（字节）
409 |             const estimates = {
410 |                 turnSize: 500, // 每个回合约500字节
411 |                 eventSize: 1000, // 每个事件约1KB
412 |                 responseSize: 2000, // 每个响应约2KB
413 |                 memberSize: 300, // 每个成员约300字节
414 |                 channelSize: 200, // 每个频道约200字节
415 |                 userSize: 250, // 每个用户约250字节
416 |             };
417 |
418 |             const totalSize =
419 |                 stats.turns * estimates.turnSize +
420 |                 stats.events * estimates.eventSize +
421 |                 stats.responses * estimates.responseSize +
422 |                 stats.members * estimates.memberSize +
423 |                 stats.channels * estimates.channelSize +
424 |                 stats.users * estimates.userSize;
425 |
426 |             return {
427 |                 totalSizeBytes: totalSize,
428 |                 totalSizeMB: Math.round((totalSize / 1024 / 1024) * 100) / 100,
429 |                 breakdown: {
430 |                     turns: { count: stats.turns, sizeBytes: stats.turns * estimates.turnSize },
431 |                     events: { count: stats.events, sizeBytes: stats.events * estimates.eventSize },
432 |                     responses: { count: stats.responses, sizeBytes: stats.responses * estimates.responseSize },
433 |                     members: { count: stats.members, sizeBytes: stats.members * estimates.memberSize },
434 |                     channels: { count: stats.channels, sizeBytes: stats.channels * estimates.channelSize },
435 |                     users: { count: stats.users, sizeBytes: stats.users * estimates.userSize },
436 |                 },
437 |                 timestamp: new Date(),
438 |             };
439 |         } catch (error) {
440 |             this.logger.error("估算存储使用失败:", error);
441 |             throw error;
442 |         }
443 |     }
444 |
445 |     /**
446 |      * 执行数据完整性检查
447 |      */
448 |     async performIntegrityCheck(): Promise<IntegrityCheckResult> {
449 |         this.logger.info("开始数据完整性检查");
450 |
451 |         const issues: IntegrityIssue[] = [];
452 |
453 |         try {
454 |             // 检查孤立事件 - 简化检查
455 |             const [allEvents, allTurns] = await Promise.all([
456 |                 this.ctx.database.get("channel_events", {}),
457 |                 this.ctx.database.get("turns", {}),
458 |             ]);
459 |
460 |             const turnIds = new Set(allTurns.map((t) => t.id));
461 |             const orphanedEvents = allEvents.filter((e) => !turnIds.has(e.turnId));
462 |
463 |             if (orphanedEvents.length > 0) {
464 |                 issues.push({
465 |                     type: "orphaned_events",
466 |                     description: `发现 ${orphanedEvents.length} 个孤立事件`,
467 |                     severity: "warning",
468 |                     count: orphanedEvents.length,
469 |                 });
470 |             }
471 |
472 |             // 检查孤立响应
473 |             const allResponses = await this.ctx.database.get("agent_responses", {});
474 |             const orphanedResponses = allResponses.filter((r) => !turnIds.has(r.turnId));
475 |
476 |             if (orphanedResponses.length > 0) {
477 |                 issues.push({
478 |                     type: "orphaned_responses",
479 |                     description: `发现 ${orphanedResponses.length} 个孤立响应`,
480 |                     severity: "warning",
481 |                     count: orphanedResponses.length,
482 |                 });
483 |             }
484 |
485 |             // 检查无效成员
486 |             const [allMembers, allUsers] = await Promise.all([this.ctx.database.get("members", {}), this.ctx.database.get("user", {})]);
487 |
488 |             const userIds = new Set(allUsers.map((u) => u.id));
489 |             const invalidMembers = allMembers.filter((m) => !userIds.has(m.userId));
490 |
491 |             if (invalidMembers.length > 0) {
492 |                 issues.push({
493 |                     type: "invalid_members",
494 |                     description: `发现 ${invalidMembers.length} 个无效成员记录`,
495 |                     severity: "error",
496 |                     count: invalidMembers.length,
497 |                 });
498 |             }
499 |         } catch (error) {
500 |             this.logger.error("数据完整性检查失败:", error);
501 |             issues.push({
502 |                 type: "check_error",
503 |                 description: `完整性检查过程中发生错误: ${error.message}`,
504 |                 severity: "error",
505 |                 count: 0,
506 |             });
507 |         }
508 |
509 |         const result: IntegrityCheckResult = {
510 |             timestamp: new Date(),
511 |             totalIssues: issues.length,
512 |             issues,
513 |             status: issues.some((i) => i.severity === "error") ? "error" : issues.some((i) => i.severity === "warning") ? "warning" : "ok",
514 |         };
515 |
516 |         this.logger.info(`数据完整性检查完成，发现 ${result.totalIssues} 个问题`);
517 |         return result;
518 |     }
519 | }
520 |
521 | // 类型定义
522 | export interface CleanupResult {
523 |     startTime: Date;
524 |     endTime: Date | null;
525 |     totalProcessed: number;
526 |     totalDeleted: number;
527 |     details: {
528 |         turns: CleanupStageResult;
529 |         events: CleanupStageResult;
530 |         responses: CleanupStageResult;
531 |         members: CleanupStageResult;
532 |         orphaned: CleanupStageResult;
533 |     };
534 |     errors: CleanupError[];
535 | }
536 |
537 | export interface CleanupStageResult {
538 |     processed: number;
539 |     deleted: number;
540 | }
541 |
542 | export interface CleanupError {
543 |     stage: string;
544 |     error: string;
545 |     timestamp: Date;
546 | }
547 |
548 | export interface DatabaseStats {
549 |     turns: number;
550 |     events: number;
551 |     responses: number;
552 |     members: number;
553 |     channels: number;
554 |     users: number;
555 |     timestamp: Date;
556 | }
557 |
558 | export interface GrowthTrend {
559 |     period: { start: Date; end: Date };
560 |     dailyTurns: Array<{ date: string; turn_count: number; active_channels: number }>;
561 |     dailyEvents: Array<{ date: string; event_count: number }>;
562 |     avgTurnsPerDay: number;
563 |     avgEventsPerDay: number;
564 | }
565 |
566 | export interface StorageEstimate {
567 |     totalSizeBytes: number;
568 |     totalSizeMB: number;
569 |     breakdown: {
570 |         [key: string]: { count: number; sizeBytes: number };
571 |     };
572 |     timestamp: Date;
573 | }
574 |
575 | export interface IntegrityCheckResult {
576 |     timestamp: Date;
577 |     totalIssues: number;
578 |     issues: IntegrityIssue[];
579 |     status: "ok" | "warning" | "error";
580 | }
581 |
582 | export interface IntegrityIssue {
583 |     type: string;
584 |     description: string;
585 |     severity: "info" | "warning" | "error";
586 |     count: number;
587 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/DataManager.ts">
  1 | import { Context, randomId, Service, Session } from "koishi";
  2 | import { AgentResponse, Channel, MemberSummary, WorldState } from "./interfaces";
  3 | import { AgentResponseData, ChannelEventData, TurnData } from "./model";
  4 | import { MemberRepository } from "./repositories/MemberRepository";
  5 | import { TurnRepository } from "./repositories/TurnRepository";
  6 | import { WorldStateConfig } from "./config";
  7 | import { BackgroundTaskManager } from "./background-task-manager";
  8 | import { TurnManager } from "./turn-manager";
  9 | import { MessageClassifier, MessageClassification } from "./message-classifier";
 10 | import { DataCleanupService } from "./data-cleanup-service";
 11 |
 12 | declare module "koishi" {
 13 |     interface Context {
 14 |         "yesimbot.data": DataManager;
 15 |     }
 16 | }
 17 |
 18 | export class DataManager extends Service {
 19 |     public readonly members: MemberRepository;
 20 |     public readonly turns: TurnRepository;
 21 |
 22 |     // 新增功能模块
 23 |     private readonly backgroundTaskManager: BackgroundTaskManager;
 24 |     private readonly turnManager: TurnManager;
 25 |     private readonly messageClassifier: MessageClassifier;
 26 |     private readonly dataCleanupService: DataCleanupService;
 27 |
 28 |     private worldStateConfig: WorldStateConfig;
 29 |
 30 |     constructor(ctx: Context, config: WorldStateConfig) {
 31 |         super(ctx, "yesimbot.data", true);
 32 |         this.worldStateConfig = config;
 33 |
 34 |         // 应用所有数据库模型定义
 35 |         ctx.plugin(require("./model"));
 36 |
 37 |         // 初始化所有 repositories
 38 |         this.members = new MemberRepository(ctx);
 39 |         this.turns = new TurnRepository(ctx, this.members);
 40 |
 41 |         // 初始化功能模块
 42 |         this.backgroundTaskManager = new BackgroundTaskManager(ctx, this.worldStateConfig, this);
 43 |         this.turnManager = new TurnManager(ctx, this.worldStateConfig);
 44 |         this.messageClassifier = new MessageClassifier(ctx, this.worldStateConfig);
 45 |         this.dataCleanupService = new DataCleanupService(ctx, this.worldStateConfig);
 46 |
 47 |         // 启动后台任务
 48 |         this.backgroundTaskManager.start();
 49 |
 50 |         // 注册停止处理
 51 |         ctx.on("dispose", () => {
 52 |             this.backgroundTaskManager.stop();
 53 |         });
 54 |     }
 55 |
 56 |     /**
 57 |      * 获取一个频道的完整运行时对象
 58 |      */
 59 |     async getFullChannel(platform: string, channelId: string): Promise<Channel | null> {
 60 |         // 1. 获取基础频道数据
 61 |         const [channelData] = await this.ctx.database.get("channel", { platform, id: channelId });
 62 |         if (!channelData) return null;
 63 |
 64 |         // 2. 使用 Repositories 获取关联数据，并应用优化
 65 |         const members = await this.members.getFullMembers(platform, channelId);
 66 |         const history = await this.turnManager.getOptimizedTurns(platform, channelId);
 67 |
 68 |         // 3. 组装 MemberSummary
 69 |         const memberSummary: MemberSummary = {
 70 |             total_count: channelData.totalMemberCount,
 71 |             recent_active_members_count: channelData.recentActiveCount,
 72 |             // online_count 是一个纯运行时状态，无法从数据库直接获取
 73 |             // 通常需要通过适配器API实时查询，或从心跳/presence事件中维护
 74 |             online_count: 0,
 75 |         };
 76 |
 77 |         // 4. 组合成最终的 Channel 对象
 78 |         return {
 79 |             id: channelData.id,
 80 |             platform: channelData.platform,
 81 |             name: channelData.name,
 82 |             type: channelData.type,
 83 |             meta: {
 84 |                 description: channelData.description,
 85 |             },
 86 |             members,
 87 |             history,
 88 |             memberSummary,
 89 |         };
 90 |     }
 91 |
 92 |     /**
 93 |      * 获取当前的世界状态（增强版）
 94 |      */
 95 |     async getWorldState(allowedChannels: string[]): Promise<WorldState> {
 96 |         const activeThreshold = new Date(Date.now() - 1 * 60 * 60 * 1000);
 97 |
 98 |         const allChannels = await this.ctx.database.get("channel", { id: allowedChannels });
 99 |
100 |         const activeChannelPromises: Promise<Channel>[] = [];
101 |         const inactiveChannelPromises: Promise<Channel>[] = [];
102 |
103 |         // 应用渲染配置限制
104 |         let activeCount = 0;
105 |
106 |         for (const chan of allChannels) {
107 |             const promise = this.getFullChannel(chan.platform, chan.id);
108 |             if (chan.lastActivityAt > activeThreshold && activeCount < this.worldStateConfig.Rendering.ActiveChannelLimit) {
109 |                 activeChannelPromises.push(promise);
110 |                 activeCount++;
111 |             } else {
112 |                 inactiveChannelPromises.push(promise);
113 |             }
114 |         }
115 |
116 |         return {
117 |             timestamp: new Date().toISOString(),
118 |             activeChannels: (await Promise.all(activeChannelPromises)).filter((c) => c !== null),
119 |             inactiveChannels: (await Promise.all(inactiveChannelPromises)).filter((c) => c !== null),
120 |         };
121 |     }
122 |
123 |     async getLastTurn(platform: string, channelId: string): Promise<TurnData> {
124 |         const [turn] = await this.ctx.database
125 |             .select("turns")
126 |             .where({
127 |                 platform,
128 |                 channelId,
129 |                 status: "new",
130 |             })
131 |             .orderBy("startTimestamp", "desc")
132 |             .limit(1)
133 |             .execute();
134 |         return turn;
135 |     }
136 |
137 |     /**
138 |      * 开始一个新的对话回合 (Turn)。
139 |      * @param platform - 平台
140 |      * @param channelId - 频道ID
141 |      * @returns 返回新创建的 TurnData 对象。
142 |      */
143 |     async startNewTurn(platform: string, channelId: string): Promise<TurnData> {
144 |         const newTurn: Partial<TurnData> = {
145 |             id: randomId(),
146 |             platform,
147 |             channelId,
148 |             status: "new",
149 |             summary: "",
150 |             startTimestamp: new Date(),
151 |             endTimestamp: null, // 尚未结束
152 |         };
153 |         return await this.ctx.database.create("turns", newTurn);
154 |     }
155 |
156 |     /**
157 |      * 向指定的 Turn 添加一条消息事件（增强版，使用消息分类器）
158 |      * @param turnId - 目标 Turn 的 ID
159 |      * @param session - 触发消息的 Koishi Session 对象
160 |      * @returns 返回新创建的 ChannelEventData 对象或 null（如果消息被过滤）
161 |      */
162 |     async addMessageEvent(turnId: string, session: Session): Promise<ChannelEventData | null> {
163 |         // 使用消息分类器分析消息
164 |         const classification = this.messageClassifier.classifyMessage(session);
165 |
166 |         // 如果消息不应该存储，直接返回 null
167 |         if (!classification.shouldStore) {
168 |             this.ctx.logger("worldstate").debug(`消息被过滤：${classification.type} - ${classification.category}`);
169 |             return null;
170 |         }
171 |
172 |         // 创建事件记录
173 |         return await this.ctx.database.create("channel_events", {
174 |             turnId,
175 |             type: classification.type,
176 |             timestamp: new Date(session.timestamp),
177 |             data: classification.eventData,
178 |         });
179 |     }
180 |
181 |     /**
182 |      * 向指定的 Turn 添加一个通用事件。
183 |      * @param turnId - 目标 Turn 的 ID
184 |      * @param type - 事件类型
185 |      * @param data - 事件的特定数据
186 |      * @returns 返回新创建的 ChannelEventData 对象。
187 |      */
188 |     async addGenericEvent(turnId: string, type: string, data: object): Promise<ChannelEventData> {
189 |         return await this.ctx.database.create("channel_events", {
190 |             turnId,
191 |             type,
192 |             timestamp: new Date(),
193 |             data,
194 |         });
195 |     }
196 |
197 |     /**
198 |      * 结束一个对话回合。
199 |      * @param turnId - 要结束的 Turn 的 ID
200 |      * @param summary - (可选) 对该回合的AI摘要
201 |      */
202 |     async endTurn(turnId: string, summary?: string): Promise<void> {
203 |         await this.ctx.database.upsert("turns", [
204 |             {
205 |                 id: turnId,
206 |                 status: summary ? "summarized" : "full",
207 |                 summary: summary,
208 |                 endTimestamp: new Date(),
209 |             },
210 |         ]);
211 |     }
212 |
213 |     /**
214 |      * 向指定的 Turn 添加一个完整的 Agent 响应。
215 |      * @param turnId - 目标 Turn 的 ID
216 |      * @param response - AgentResponse 业务对象
217 |      * @returns 返回新创建的 AgentResponseData 对象。
218 |      */
219 |     async addAgentResponse(turnId: string, response: AgentResponse): Promise<AgentResponseData> {
220 |         const { thoughts, actions, observations } = response;
221 |         return await this.ctx.database.create("agent_responses", {
222 |             turnId,
223 |             thoughts,
224 |             actions,
225 |             observations,
226 |         });
227 |     }
228 |
229 |     /**
230 |      * 根据 Session "顺便" 更新频道信息。
231 |      * 这应该在每次收到消息时调用。
232 |      * @param session - Koishi Session 对象
233 |      */
234 |     async touchChannel(session: Session): Promise<void> {
235 |         // session.guild 包含适配器获取的最新群组信息
236 |         //@ts-ignore
237 |         const channelName = session.guild?.name ?? session.channel.name;
238 |
239 |         await this.ctx.database.upsert("channel", [
240 |             {
241 |                 id: session.channelId,
242 |                 platform: session.platform,
243 |                 // 更新频道名称，以防它被修改
244 |                 name: channelName,
245 |                 // 更新最后活动时间
246 |                 lastActivityAt: new Date(),
247 |             },
248 |         ]);
249 |     }
250 |
251 |     /**
252 |      * 当成员列表发生变化时，更新频道的成员计数值。
253 |      * 这应该在 'guild-member-added' 或 'guild-member-removed' 事件中调用。
254 |      * @param guildId - 频道/群组 ID
255 |      * @param platform - 平台
256 |      */
257 |     async updateChannelMemberCount(guildId: string, platform: string) {
258 |         // 尝试从适配器获取最新的成员总数
259 |         const bot = this.ctx.bots[`${platform}:${this.ctx.config.selfId}`];
260 |         let totalCount = 0;
261 |         try {
262 |             // 注意: getGuild 方法和返回的成员数取决于具体适配器
263 |             const guild = await bot?.getGuild(guildId);
264 |             //@ts-ignore
265 |             totalCount = guild?.memberCount ?? 0;
266 |         } catch (error) {
267 |             // 如果API调用失败，可以从我们自己的 members 表中计数作为备用方案
268 |             //@ts-ignore
269 |             totalCount = await this.ctx.database.count("members", { channelId: guildId, platform });
270 |             this.ctx.logger("data").warn(`Failed to fetch member count for ${guildId}, using fallback count: ${totalCount}`);
271 |         }
272 |
273 |         await this.ctx.database.upsert("channel", [
274 |             {
275 |                 id: guildId,
276 |                 platform: platform,
277 |                 totalMemberCount: totalCount,
278 |             },
279 |         ]);
280 |     }
281 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/index.ts">
 1 | export * from "./background-task-manager";
 2 | export * from "./config";
 3 | export * from "./data-cleanup-service";
 4 | export * from "./DataManager";
 5 | export * from "./interfaces";
 6 | export * from "./message-classifier";
 7 | export * from "./model";
 8 | export * from "./repositories/MemberRepository";
 9 | export * from "./repositories/TurnRepository";
10 | export * from "./turn-manager";
11 |

</file_content>

<file_content path="packages/core/src/services/worldstate/interfaces.ts">
  1 | import { ToolCallResult } from "../extensions";
  2 |
  3 | export interface WorldState {
  4 |     timestamp: string;
  5 |     activeChannels: Channel[];
  6 |     inactiveChannels: Channel[];
  7 | }
  8 |
  9 | // 一个频道对象，替换原来的 Scenario
 10 | // 群组或是私聊，或者沙盒测试环境
 11 | export interface Channel {
 12 |     id: string; // 频道 ID
 13 |     name: string; // 频道名称，群聊就是群组名，私聊为“你和 <用户名> 的私聊”
 14 |     type: "guild" | "private" | "sandbox";
 15 |     platform: string;
 16 |     meta: {
 17 |         description?: string; // 频道描述，有些适配器获取不到。或许可以根据历史对话生成一个
 18 |     };
 19 |     // 经过智能筛选和摘要的成员信息
 20 |     // 层次1: 核心成员，如群主、管理员，或与自己有特殊关系的成员
 21 |     // 层次2: 上下文相关成员 (近期发言或被@)
 22 |     members: Member[];
 23 |
 24 |     // 层次3: 群体氛围感知的摘要信息
 25 |     memberSummary: MemberSummary;
 26 |     history: Turn[];
 27 | }
 28 |
 29 | export interface MemberSummary {
 30 |     total_count: number; // 频道成员总数
 31 |     online_count: number; // 频道在线成员数
 32 |     recent_active_members_count: number; // 频道近期活跃成员数
 33 | }
 34 |
 35 | export interface User {
 36 |     id: string; // 特点平台用户 ID (pid)
 37 |     name: string; // 用户名称
 38 |     meta: {
 39 |         avatar?: string; // 用户头像 URL
 40 |         [key: string]: unknown;
 41 |     };
 42 |     created_at: Date;
 43 |     updated_at: Date;
 44 | }
 45 |
 46 | export interface Member extends User {
 47 |     channel_id: string;
 48 |     meta: User["meta"] & {
 49 |         nick?: string;
 50 |         role?: string;
 51 |     };
 52 |     last_active?: string; // 用户上次活跃时间
 53 | }
 54 |
 55 | export interface Turn {
 56 |     id: string;
 57 |     status: "full" | "summarized" | "folded" | "new";
 58 |     events: ChannelEvent[];
 59 |     summary?: string; // 摘要
 60 |     responses: AgentResponse[];
 61 | }
 62 |
 63 | export interface AgentResponse {
 64 |     thoughts: Thought;
 65 |     actions: Action[];
 66 |     observations: ActionResult[];
 67 | }
 68 |
 69 | export interface Thought {
 70 |     obverse: string;
 71 |     analyze_infer: string;
 72 |     plan: string;
 73 | }
 74 |
 75 | export interface Action {
 76 |     function: string;
 77 |     params: Record<string, unknown>;
 78 |     renderParams?: () => string;
 79 | }
 80 |
 81 | export interface ActionResult {
 82 |     function: string;
 83 |     result: ToolCallResult;
 84 |     renderResult?: () => string;
 85 | }
 86 |
 87 | // --- 事件相关接口 ---
 88 |
 89 | // 基础事件结构
 90 | interface BaseEvent {
 91 |     id: number; // 自增 ID
 92 |     type: string;
 93 |     timestamp: Date;
 94 | }
 95 |
 96 | // 具体事件类型定义
 97 | export interface UserJoinedEvent extends BaseEvent {
 98 |     type: "user_joined";
 99 |     actor: Member; // 操作者 (可能是系统或其他成员)
100 |     user: Member; // 加入的成员
101 |     note?: string;
102 | }
103 |
104 | export interface UserLeftEvent extends BaseEvent {
105 |     type: "user_left";
106 |     actor: Member;
107 |     user: Member;
108 |     reason?: string;
109 | }
110 |
111 | export interface MessageEvent extends BaseEvent {
112 |     type: "message";
113 |     messageId: string;
114 |     sender: Member;
115 |     content: string;
116 | }
117 |
118 | export interface SystemNotificationEvent extends BaseEvent {
119 |     type: "system_notification";
120 |     content: string;
121 | }
122 |
123 | export type ChannelEvent = UserJoinedEvent | UserLeftEvent | MessageEvent | SystemNotificationEvent;

</file_content>

<file_content path="packages/core/src/services/worldstate/message-classifier.ts">
  1 | import { Context, Logger, Session } from "koishi";
  2 | import { WorldStateConfig } from "./config";
  3 |
  4 | /**
  5 |  * 消息分类器
  6 |  * 负责准确区分事件消息和用户消息，优化数据存储结构
  7 |  */
  8 | export class MessageClassifier {
  9 |     private logger: Logger;
 10 |
 11 |     constructor(private ctx: Context, private config: WorldStateConfig) {
 12 |         this.logger = ctx.logger("worldstate:classifier");
 13 |     }
 14 |
 15 |     /**
 16 |      * 分类消息类型
 17 |      */
 18 |     classifyMessage(session: Session): MessageClassification {
 19 |         const content = session.content;
 20 |         const messageType = this.determineMessageType(session);
 21 |         const category = this.determineCategory(session, messageType);
 22 |         const priority = this.determinePriority(session, category);
 23 |
 24 |         return {
 25 |             type: messageType,
 26 |             category,
 27 |             priority,
 28 |             shouldStore: this.shouldStoreMessage(session, category),
 29 |             eventData: this.extractEventData(session, messageType),
 30 |             metadata: this.extractMetadata(session),
 31 |         };
 32 |     }
 33 |
 34 |     /**
 35 |      * 确定消息基础类型
 36 |      */
 37 |     private determineMessageType(session: Session): MessageType {
 38 |         // 检查是否为系统事件
 39 |         if (this.isSystemEvent(session)) {
 40 |             return this.classifySystemEvent(session);
 41 |         }
 42 |
 43 |         // 检查是否为用户消息
 44 |         if (this.isUserMessage(session)) {
 45 |             return this.classifyUserMessage(session);
 46 |         }
 47 |
 48 |         // 检查是否为机器人消息
 49 |         if (this.isBotMessage(session)) {
 50 |             return "bot_message";
 51 |         }
 52 |
 53 |         // 默认为未知类型
 54 |         return "unknown";
 55 |     }
 56 |
 57 |     /**
 58 |      * 判断是否为系统事件
 59 |      */
 60 |     private isSystemEvent(session: Session): boolean {
 61 |         // 检查事件类型
 62 |         const eventTypes = [
 63 |             "guild-member-added",
 64 |             "guild-member-removed",
 65 |             "guild-member-updated",
 66 |             "channel-created",
 67 |             "channel-deleted",
 68 |             "channel-updated",
 69 |             "message-deleted",
 70 |             "message-updated",
 71 |         ];
 72 |
 73 |         return eventTypes.includes(session.type);
 74 |     }
 75 |
 76 |     /**
 77 |      * 分类系统事件类型
 78 |      */
 79 |     private classifySystemEvent(session: Session): MessageType {
 80 |         switch (session.type) {
 81 |             case "guild-member-added":
 82 |                 return "user_joined";
 83 |             case "guild-member-removed":
 84 |                 return "user_left";
 85 |             case "guild-member-updated":
 86 |                 return "member_updated";
 87 |             case "channel-created":
 88 |             case "channel-deleted":
 89 |             case "channel-updated":
 90 |                 return "channel_updated";
 91 |             case "message-deleted":
 92 |             case "message-updated":
 93 |                 return "message_updated";
 94 |             default:
 95 |                 return "system_notification";
 96 |         }
 97 |     }
 98 |
 99 |     /**
100 |      * 判断是否为用户消息
101 |      */
102 |     private isUserMessage(session: Session): boolean {
103 |         return session.type === "message" && session.userId !== session.selfId && !this.isBotUser(session);
104 |     }
105 |
106 |     /**
107 |      * 分类用户消息类型
108 |      */
109 |     private classifyUserMessage(session: Session): MessageType {
110 |         const content = session.content?.trim();
111 |
112 |         if (!content) {
113 |             return "empty_message";
114 |         }
115 |
116 |         // 检查是否为命令
117 |         if (content.startsWith("/") || content.startsWith("!")) {
118 |             return "command_message";
119 |         }
120 |
121 |         // 检查是否为@消息
122 |         if (this.isAtMessage(session)) {
123 |             return "mention_message";
124 |         }
125 |
126 |         // 检查是否为回复消息
127 |         if (session.quote) {
128 |             return "reply_message";
129 |         }
130 |
131 |         // 检查消息长度
132 |         if (content.length < this.config.MessageProcessing.Filtering.MinMessageLength) {
133 |             return "short_message";
134 |         }
135 |
136 |         return "user_message";
137 |     }
138 |
139 |     /**
140 |      * 判断是否为机器人消息
141 |      */
142 |     private isBotMessage(session: Session): boolean {
143 |         return session.userId === session.selfId || this.isBotUser(session);
144 |     }
145 |
146 |     /**
147 |      * 判断是否为机器人用户
148 |      */
149 |     private isBotUser(session: Session): boolean {
150 |         // 可以通过用户名模式或其他特征识别机器人
151 |         const botPatterns = [/bot$/i, /^bot/i, /助手$/, /机器人$/];
152 |
153 |         const username = session.username || session.author?.name || "";
154 |         return botPatterns.some((pattern) => pattern.test(username));
155 |     }
156 |
157 |     /**
158 |      * 判断是否为@消息
159 |      */
160 |     private isAtMessage(session: Session): boolean {
161 |         return session.stripped.atSelf;
162 |     }
163 |
164 |     /**
165 |      * 确定消息分类
166 |      */
167 |     private determineCategory(session: Session, messageType: MessageType): MessageCategory {
168 |         // 系统事件类别
169 |         const systemEvents: MessageType[] = ["user_joined", "user_left", "member_updated", "channel_updated", "system_notification"];
170 |
171 |         if (systemEvents.includes(messageType)) {
172 |             return "system_event";
173 |         }
174 |
175 |         // 用户交互类别
176 |         const userInteractions: MessageType[] = ["user_message", "mention_message", "reply_message", "command_message"];
177 |
178 |         if (userInteractions.includes(messageType)) {
179 |             return "user_interaction";
180 |         }
181 |
182 |         // 机器人响应类别
183 |         if (messageType === "bot_message") {
184 |             return "bot_response";
185 |         }
186 |
187 |         // 元数据类别（不重要的消息）
188 |         const metadataTypes: MessageType[] = ["empty_message", "short_message", "message_updated", "unknown"];
189 |
190 |         if (metadataTypes.includes(messageType)) {
191 |             return "metadata";
192 |         }
193 |
194 |         return "other";
195 |     }
196 |
197 |     /**
198 |      * 确定消息优先级
199 |      */
200 |     private determinePriority(session: Session, category: MessageCategory): MessagePriority {
201 |         switch (category) {
202 |             case "user_interaction":
203 |                 // @消息和回复消息优先级最高
204 |                 if (this.isAtMessage(session) || session.quote) {
205 |                     return "high";
206 |                 }
207 |                 return "medium";
208 |
209 |             case "system_event":
210 |                 return "medium";
211 |
212 |             case "bot_response":
213 |                 return "low";
214 |
215 |             case "metadata":
216 |                 return "very_low";
217 |
218 |             default:
219 |                 return "low";
220 |         }
221 |     }
222 |
223 |     /**
224 |      * 判断是否应该存储消息
225 |      */
226 |     private shouldStoreMessage(session: Session, category: MessageCategory): boolean {
227 |         // 检查是否在忽略列表中
228 |         const ignoredTypes = this.config.MessageProcessing.Filtering.IgnoredMessageTypes;
229 |         if (ignoredTypes.includes(session.type)) {
230 |             return false;
231 |         }
232 |
233 |         // 根据分类决定是否存储
234 |         switch (category) {
235 |             case "user_interaction":
236 |             case "system_event":
237 |                 return true;
238 |
239 |             case "bot_response":
240 |                 // 只存储重要的机器人响应
241 |                 return this.isImportantBotResponse(session);
242 |
243 |             case "metadata":
244 |                 // 通常不存储元数据类消息
245 |                 return false;
246 |
247 |             default:
248 |                 return false;
249 |         }
250 |     }
251 |
252 |     /**
253 |      * 判断是否为重要的机器人响应
254 |      */
255 |     private isImportantBotResponse(session: Session): boolean {
256 |         const content = session.content;
257 |
258 |         // 长消息通常比较重要
259 |         if (content.length > 100) {
260 |             return true;
261 |         }
262 |
263 |         // 包含特定关键词的响应
264 |         const importantKeywords = ["错误", "警告", "成功", "完成", "失败", "error", "warning", "success", "complete", "failed"];
265 |
266 |         return importantKeywords.some((keyword) => content.toLowerCase().includes(keyword.toLowerCase()));
267 |     }
268 |
269 |     /**
270 |      * 提取事件数据
271 |      */
272 |     private extractEventData(session: Session, messageType: MessageType): Record<string, any> {
273 |         const baseData = {
274 |             messageId: session.messageId,
275 |             userId: session.userId,
276 |             timestamp: new Date(session.timestamp),
277 |         };
278 |
279 |         switch (messageType) {
280 |             case "user_message":
281 |             case "mention_message":
282 |             case "reply_message":
283 |             case "command_message":
284 |                 return {
285 |                     ...baseData,
286 |                     senderId: session.userId,
287 |                     content: session.content,
288 |                     quote: session.quote
289 |                         ? {
290 |                               messageId: session.quote.messageId,
291 |                               content: session.quote.content,
292 |                           }
293 |                         : null,
294 |                 };
295 |
296 |             case "user_joined":
297 |                 return {
298 |                     ...baseData,
299 |                     actorId: session.operatorId || "system",
300 |                     userId: session.userId,
301 |                     note: this.extractJoinReason(session),
302 |                 };
303 |
304 |             case "user_left":
305 |                 return {
306 |                     ...baseData,
307 |                     actorId: session.operatorId || "system",
308 |                     userId: session.userId,
309 |                     reason: this.extractLeaveReason(session),
310 |                 };
311 |
312 |             case "member_updated":
313 |                 return {
314 |                     ...baseData,
315 |                     userId: session.userId,
316 |                     changes: this.extractMemberChanges(session),
317 |                 };
318 |
319 |             case "system_notification":
320 |                 return {
321 |                     ...baseData,
322 |                     content: session.content || "系统通知",
323 |                     notificationType: this.extractNotificationType(session),
324 |                 };
325 |
326 |             default:
327 |                 return baseData;
328 |         }
329 |     }
330 |
331 |     /**
332 |      * 提取加入原因
333 |      */
334 |     private extractJoinReason(session: Session): string | null {
335 |         // 从session中提取加入原因
336 |         return session.content || null;
337 |     }
338 |
339 |     /**
340 |      * 提取离开原因
341 |      */
342 |     private extractLeaveReason(session: Session): string | null {
343 |         // 从session中提取离开原因
344 |         return session.content || null;
345 |     }
346 |
347 |     /**
348 |      * 提取成员变更信息
349 |      */
350 |     private extractMemberChanges(session: Session): Record<string, any> {
351 |         // 提取成员信息变更的具体内容
352 |         return {
353 |             oldValue: session.content, // 简化处理
354 |             newValue: session.content,
355 |             changeType: "unknown",
356 |         };
357 |     }
358 |
359 |     /**
360 |      * 提取通知类型
361 |      */
362 |     private extractNotificationType(session: Session): string {
363 |         // 根据内容判断通知类型
364 |         const content = session.content?.toLowerCase() || "";
365 |
366 |         if (content.includes("禁言") || content.includes("mute")) {
367 |             return "mute";
368 |         }
369 |         if (content.includes("解禁") || content.includes("unmute")) {
370 |             return "unmute";
371 |         }
372 |         if (content.includes("管理员") || content.includes("admin")) {
373 |             return "admin_action";
374 |         }
375 |
376 |         return "general";
377 |     }
378 |
379 |     /**
380 |      * 提取元数据
381 |      */
382 |     private extractMetadata(session: Session): MessageMetadata {
383 |         return {
384 |             platform: session.platform,
385 |             channelId: session.channelId,
386 |             //@ts-ignore
387 |             channelName: session.channel?.name,
388 |             authorName: session.username || session.author?.name,
389 |             authorAvatar: session.author?.avatar,
390 |             //@ts-ignore
391 |             isPrivate: session.channel?.type === "private",
392 |             hasAttachments: !!(session.elements && session.elements.length > 0),
393 |             wordCount: session.content ? session.content.length : 0,
394 |             language: this.detectLanguage(session.content || ""),
395 |         };
396 |     }
397 |
398 |     /**
399 |      * 检测语言（简化版）
400 |      */
401 |     private detectLanguage(content: string): string {
402 |         // 简化的语言检测
403 |         const chinesePattern = /[\u4e00-\u9fff]/;
404 |         const englishPattern = /[a-zA-Z]/;
405 |
406 |         if (chinesePattern.test(content)) {
407 |             return "zh";
408 |         }
409 |         if (englishPattern.test(content)) {
410 |             return "en";
411 |         }
412 |         return "unknown";
413 |     }
414 |
415 |     /**
416 |      * 批量分类消息
417 |      */
418 |     async classifyBatch(sessions: Session[]): Promise<MessageClassification[]> {
419 |         return sessions.map((session) => this.classifyMessage(session));
420 |     }
421 |
422 |     /**
423 |      * 获取分类统计
424 |      */
425 |     getClassificationStats(classifications: MessageClassification[]): ClassificationStats {
426 |         const stats: ClassificationStats = {
427 |             total: classifications.length,
428 |             byCategory: new Map(),
429 |             byType: new Map(),
430 |             byPriority: new Map(),
431 |             stored: 0,
432 |             filtered: 0,
433 |         };
434 |
435 |         for (const classification of classifications) {
436 |             // 统计分类
437 |             stats.byCategory.set(classification.category, (stats.byCategory.get(classification.category) || 0) + 1);
438 |
439 |             // 统计类型
440 |             stats.byType.set(classification.type, (stats.byType.get(classification.type) || 0) + 1);
441 |
442 |             // 统计优先级
443 |             stats.byPriority.set(classification.priority, (stats.byPriority.get(classification.priority) || 0) + 1);
444 |
445 |             // 统计存储状态
446 |             if (classification.shouldStore) {
447 |                 stats.stored++;
448 |             } else {
449 |                 stats.filtered++;
450 |             }
451 |         }
452 |
453 |         return stats;
454 |     }
455 | }
456 |
457 | // 类型定义
458 | export type MessageType =
459 |     | "user_message"
460 |     | "mention_message"
461 |     | "reply_message"
462 |     | "command_message"
463 |     | "user_joined"
464 |     | "user_left"
465 |     | "member_updated"
466 |     | "channel_updated"
467 |     | "system_notification"
468 |     | "bot_message"
469 |     | "empty_message"
470 |     | "short_message"
471 |     | "message_updated"
472 |     | "unknown";
473 |
474 | export type MessageCategory = "user_interaction" | "system_event" | "bot_response" | "metadata" | "other";
475 |
476 | export type MessagePriority = "very_high" | "high" | "medium" | "low" | "very_low";
477 |
478 | export interface MessageClassification {
479 |     type: MessageType;
480 |     category: MessageCategory;
481 |     priority: MessagePriority;
482 |     shouldStore: boolean;
483 |     eventData: Record<string, any>;
484 |     metadata: MessageMetadata;
485 | }
486 |
487 | export interface MessageMetadata {
488 |     platform: string;
489 |     channelId: string;
490 |     channelName?: string;
491 |     authorName?: string;
492 |     authorAvatar?: string;
493 |     isPrivate: boolean;
494 |     hasAttachments: boolean;
495 |     wordCount: number;
496 |     language: string;
497 | }
498 |
499 | export interface ClassificationStats {
500 |     total: number;
501 |     byCategory: Map<MessageCategory, number>;
502 |     byType: Map<MessageType, number>;
503 |     byPriority: Map<MessagePriority, number>;
504 |     stored: number;
505 |     filtered: number;
506 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/model.ts">
  1 | import { Context } from "koishi";
  2 | import { Action, ActionResult, Thought, Turn } from "./interfaces";
  3 |
  4 | // --- Data Transfer Objects (DTOs) / Database Table Schemas ---
  5 | // 这些接口精确匹配数据库中的一行数据
  6 |
  7 | export interface MemberData {
  8 |     userId: number;
  9 |     platform: string;
 10 |     channelId: string;
 11 |     nick: string;
 12 |     role: string;
 13 |     lastActive: Date;
 14 | }
 15 |
 16 | export interface TurnData {
 17 |     id: string;
 18 |     channelId: string;
 19 |     platform: string;
 20 |     status: Turn["status"];
 21 |     summary: string;
 22 |     startTimestamp: Date;
 23 |     endTimestamp: Date;
 24 | }
 25 |
 26 | export interface AgentResponseData {
 27 |     id: number;
 28 |     turnId: string;
 29 |     thoughts: Thought;
 30 |     actions: Action[];
 31 |     observations: ActionResult[];
 32 | }
 33 |
 34 | export interface ChannelEventData {
 35 |     id: number; // 自增主键，用于唯一标识和排序
 36 |     turnId: string; // 外键，关联到 Turn
 37 |     type: string; // 事件类型，如 'user_joined', 'message_sent'
 38 |     timestamp: Date; // 事件发生时间
 39 |     data: object; // JSON 字段，存储该事件类型的特定数据
 40 | }
 41 |
 42 | // --- Koishi-specific Table Augmentation ---
 43 | // 扩展 Koishi 的核心接口和表定义
 44 |
 45 | declare module "koishi" {
 46 |     interface User {
 47 |         avatar?: string;
 48 |         createdAt: Date;
 49 |         updatedAt: Date;
 50 |     }
 51 |
 52 |     interface Channel {
 53 |         name?: string;
 54 |         type?: "guild" | "private" | "sandbox";
 55 |         description?: string;
 56 |         totalMemberCount?: number;
 57 |         recentActiveCount?: number;
 58 |         lastActivityAt?: Date;
 59 |     }
 60 |
 61 |     interface Tables {
 62 |         members: MemberData;
 63 |         turns: TurnData;
 64 |         channel_events: ChannelEventData;
 65 |         agent_responses: AgentResponseData;
 66 |     }
 67 | }
 68 |
 69 | export const name = "yesimbot-models";
 70 | export const inject = ["database"];
 71 |
 72 | export function apply(ctx: Context) {
 73 |     ctx.model.extend("user", {
 74 |         avatar: "string",
 75 |         createdAt: "timestamp",
 76 |         updatedAt: "timestamp",
 77 |     });
 78 |
 79 |     ctx.model.extend("channel", {
 80 |         name: "string",
 81 |         type: "string",
 82 |         description: "text",
 83 |         totalMemberCount: "unsigned",
 84 |         recentActiveCount: "unsigned",
 85 |         lastActivityAt: "timestamp",
 86 |     });
 87 |
 88 |     ctx.model.extend(
 89 |         "members",
 90 |         {
 91 |             userId: "unsigned",
 92 |             platform: "string(255)",
 93 |             channelId: "string(255)",
 94 |             nick: "string",
 95 |             role: "string",
 96 |             lastActive: "timestamp",
 97 |         },
 98 |         {
 99 |             primary: ["userId", "platform", "channelId"],
100 |             foreign: {
101 |                 userId: ["user", "id"],
102 |                 channelId: ["channel", "id"],
103 |                 platform: ["channel", "platform"],
104 |             },
105 |         }
106 |     );
107 |
108 |     ctx.model.extend(
109 |         "channel_events",
110 |         {
111 |             id: "unsigned",
112 |             turnId: "string(64)",
113 |             type: "string(64)",
114 |             timestamp: "timestamp",
115 |             data: "json",
116 |         },
117 |         {
118 |             autoInc: true,
119 |             primary: "id",
120 |             foreign: {
121 |                 turnId: ["turns", "id"],
122 |             },
123 |         }
124 |     );
125 |
126 |     ctx.model.extend(
127 |         "turns",
128 |         {
129 |             id: "char(64)",
130 |             channelId: "char(64)",
131 |             platform: "char(64)",
132 |             status: "string",
133 |             summary: "text",
134 |             startTimestamp: "timestamp",
135 |             endTimestamp: "timestamp",
136 |         },
137 |         {
138 |             primary: "id",
139 |             foreign: {
140 |                 channelId: ["channel", "id"],
141 |                 platform: ["channel", "platform"],
142 |             },
143 |         }
144 |     );
145 |
146 |     ctx.model.extend(
147 |         "agent_responses",
148 |         {
149 |             id: "unsigned",
150 |             turnId: "char(64)",
151 |             thoughts: "json",
152 |             actions: "json",
153 |             observations: "json",
154 |         },
155 |         {
156 |             autoInc: true,
157 |             primary: "id",
158 |             foreign: {
159 |                 turnId: ["turns", "id"],
160 |             },
161 |         }
162 |     );
163 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/README.md">
  1 | # WorldState 上下文管理器模块
  2 |
  3 | ## 概述
  4 |
  5 | WorldState 模块是为 LLM chatbot 设计的核心组件，负责收集、管理和组织群组消息、聊天记录、系统事件，并为 PromptBuilder 提供结构化数据用于渲染提示词。
  6 |
  7 | ## 模块结构
  8 |
  9 | ```
 10 | worldstate/
 11 | ├── config.ts                    # 配置驱动的架构
 12 | ├── DataManager.ts              # 主要数据管理器（增强版）
 13 | ├── interfaces.ts               # 接口定义
 14 | ├── model.ts                    # 数据模型和数据库表定义
 15 | ├── background-task-manager.ts  # 后台任务管理器
 16 | ├── turn-manager.ts             # 回合管理器
 17 | ├── message-classifier.ts       # 消息分类器
 18 | ├── data-cleanup-service.ts     # 数据清理服务
 19 | ├── repositories/               # 数据访问层
 20 | │   ├── MemberRepository.ts
 21 | │   └── TurnRepository.ts
 22 | └── README.md                   # 本文档
 23 | ```
 24 |
 25 | ## 已实现的功能改进
 26 |
 27 | ### 1. 成员和群组信息更新机制 ✅
 28 |
 29 | **实现位置**: `background-task-manager.ts`
 30 |
 31 | - **定期更新任务**: 可配置的成员信息和群组信息更新间隔
 32 | - **批量处理**: 支持批量更新，避免性能问题
 33 | - **错误处理**: 优雅处理单个更新失败的情况
 34 | - **配置选项**:
 35 |   - `DataManagement.MemberUpdate.Enabled`: 是否启用成员更新
 36 |   - `DataManagement.MemberUpdate.IntervalMinutes`: 更新间隔（分钟）
 37 |   - `DataManagement.MemberUpdate.BatchSize`: 批量大小
 38 |
 39 | ### 2. 回合管理优化 ✅
 40 |
 41 | **实现位置**: `turn-manager.ts`
 42 |
 43 | - **自动折叠**: 根据时间和事件数量自动折叠过期回合
 44 | - **智能摘要**: 自动生成回合摘要，保持上下文连续性
 45 | - **渲染优化**: 限制显示的回合数量和事件数量
 46 | - **配置选项**:
 47 |   - `TurnManagement.Lifecycle.ActiveRetentionHours`: 活跃回合保留时间
 48 |   - `TurnManagement.Lifecycle.MaxActiveTurns`: 最大活跃回合数
 49 |   - `TurnManagement.Summary.Enabled`: 是否启用自动摘要
 50 |
 51 | ### 3. 消息入库机制改进 ✅
 52 |
 53 | **实现位置**: `message-classifier.ts`
 54 |
 55 | - **智能分类**: 准确区分用户消息、系统事件、机器人响应等
 56 | - **过滤机制**: 自动过滤不重要的消息，减少存储开销
 57 | - **优先级管理**: 为不同类型的消息分配优先级
 58 | - **元数据提取**: 提取消息的详细元数据信息
 59 |
 60 | ### 4. 数据清理功能 ✅
 61 |
 62 | **实现位置**: `data-cleanup-service.ts`
 63 |
 64 | - **定期清理**: 可配置的数据清理间隔和保留期限
 65 | - **完整性检查**: 检测和修复数据完整性问题
 66 | - **存储分析**: 估算存储使用情况和增长趋势
 67 | - **批量操作**: 分批处理大量数据，避免性能问题
 68 |
 69 | ### 5. 中间件集成 ✅
 70 |
 71 | **实现位置**: `middleware/worldstate-middleware.ts`
 72 |
 73 | - **无缝集成**: 与现有中间件架构协调工作
 74 | - **回合时机管理**: 正确处理回合开始和结束时机
 75 | - **上下文传递**: 为后续中间件提供分类和状态信息
 76 | - **错误处理**: 优雅的错误处理策略
 77 |
 78 | ## 配置说明
 79 |
 80 | ### 主要配置项
 81 |
 82 | ```typescript
 83 | interface WorldStateConfig {
 84 |   DataManagement: {
 85 |     MemberUpdate: {
 86 |       Enabled: boolean;           // 是否启用成员更新
 87 |       IntervalMinutes: number;    // 更新间隔（分钟）
 88 |       BatchSize: number;          // 批量大小
 89 |     };
 90 |     ChannelUpdate: {
 91 |       Enabled: boolean;           // 是否启用群组更新
 92 |       IntervalMinutes: number;    // 更新间隔（分钟）
 93 |     };
 94 |   };
 95 |   TurnManagement: {
 96 |     Lifecycle: {
 97 |       ActiveRetentionHours: number;  // 活跃回合保留时间（小时）
 98 |       MaxActiveTurns: number;        // 最大活跃回合数
 99 |       AutoFoldExpired: boolean;      // 自动折叠过期回合
100 |     };
101 |     Summary: {
102 |       Enabled: boolean;              // 是否启用自动摘要
103 |       EventCountThreshold: number;   // 触发摘要的事件数阈值
104 |       TimeThresholdHours: number;    // 触发摘要的时间阈值（小时）
105 |     };
106 |   };
107 |   DataCleanup: {
108 |     Enabled: boolean;           // 是否启用定期清理
109 |     IntervalHours: number;      // 清理间隔（小时）
110 |     RetentionDays: number;      // 数据保留期限（天）
111 |     BatchSize: number;          // 清理批量大小
112 |   };
113 |   Rendering: {
114 |     ActiveChannelLimit: number;     // 活跃频道显示限制
115 |     MaxTurnsPerChannel: number;     // 每个频道显示的最大回合数
116 |     MaxEventsPerTurn: number;       // 每个回合显示的最大事件数
117 |   };
118 | }
119 | ```
120 |
121 | ## 使用方式
122 |
123 | ### 1. 基本使用
124 |
125 | ```typescript
126 | import { DataManager, WorldStateConfig } from "./services/worldstate";
127 |
128 | // 创建配置
129 | const config: WorldStateConfig = {
130 |   // ... 配置项
131 | };
132 |
133 | // 创建数据管理器
134 | const dataManager = new DataManager(ctx, config);
135 |
136 | // 获取世界状态
137 | const worldState = await dataManager.getWorldState(allowedChannels);
138 | ```
139 |
140 | ### 2. 中间件集成
141 |
142 | ```typescript
143 | import { WorldStateMiddleware } from "./middleware/worldstate-middleware";
144 |
145 | // 创建中间件
146 | const worldStateMiddleware = new WorldStateMiddleware(ctx, {
147 |   enableMessageClassification: true,
148 |   enableAutoEventRecording: true,
149 |   recordTurnStartEvent: true,
150 |   recordTurnEndEvent: true,
151 |   errorHandling: "continue"
152 | });
153 |
154 | // 添加到管道
155 | pipeline.use(worldStateMiddleware);
156 | ```
157 |
158 | ### 3. 手动操作
159 |
160 | ```typescript
161 | // 手动折叠回合
162 | await dataManager.foldTurn(turnId);
163 |
164 | // 手动生成摘要
165 | await dataManager.summarizeTurn(turnId);
166 |
167 | // 执行数据清理
168 | const cleanupResult = await dataManager.performDataCleanup();
169 |
170 | // 获取统计信息
171 | const stats = await dataManager.getDatabaseStats();
172 | ```
173 |
174 | ## 技术特点
175 |
176 | ### 1. 配置驱动架构
177 | - 所有功能都可通过配置启用/禁用
178 | - 避免硬编码值，提高灵活性
179 | - 支持运行时配置更新
180 |
181 | ### 2. 模块化设计
182 | - 每个类承担单一职责
183 | - 清晰的依赖关系
184 | - 易于测试和维护
185 |
186 | ### 3. 性能优化
187 | - 批量处理大量数据
188 | - 智能缓存和索引
189 | - 分页和限制机制
190 |
191 | ### 4. 错误处理
192 | - 优雅的错误处理策略
193 | - 详细的日志记录
194 | - 故障隔离机制
195 |
196 | ## 数据库表结构
197 |
198 | 模块扩展了以下 Koishi 数据库表：
199 |
200 | - `user`: 用户基础信息
201 | - `channel`: 频道/群组信息
202 | - `members`: 成员信息
203 | - `turns`: 回合数据
204 | - `channel_events`: 频道事件
205 | - `agent_responses`: AI 响应记录
206 |
207 | ## 模板渲染
208 |
209 | 配合 `world_state.mustache` 模板使用，支持：
210 |
211 | - 活跃频道和非活跃频道的区分显示
212 | - 回合状态的动态渲染
213 | - 事件类型的条件显示
214 | - 响应数据的结构化输出
215 |
216 | ## 注意事项
217 |
218 | 1. **性能考虑**: 大量数据时注意配置合适的批量大小和清理间隔
219 | 2. **存储空间**: 定期监控存储使用情况，及时清理过期数据
220 | 3. **配置调优**: 根据实际使用情况调整回合保留时间和摘要阈值
221 | 4. **错误监控**: 关注后台任务的执行状态和错误日志
222 |
223 | ## 未来扩展
224 |
225 | - 支持更多消息类型的分类
226 | - 实现更智能的回合分割算法
227 | - 添加数据导出和备份功能
228 | - 支持分布式部署的数据同步

</file_content>

<file_content path="packages/core/src/services/worldstate/turn-manager.ts">
  1 | import { Context, Logger } from "koishi";
  2 | import { WorldStateConfig } from "./config";
  3 | import { TurnData } from "./model";
  4 | import { Turn } from "./interfaces";
  5 |
  6 | /**
  7 |  * 回合管理器
  8 |  * 负责回合的生命周期管理、自动折叠、摘要生成等功能
  9 |  */
 10 | export class TurnManager {
 11 |     private logger: Logger;
 12 |
 13 |     constructor(private ctx: Context, private config: WorldStateConfig) {
 14 |         this.logger = ctx.logger("worldstate:turn");
 15 |     }
 16 |
 17 |     /**
 18 |      * 获取经过优化的回合列表
 19 |      * 自动处理过期回合的折叠和限制
 20 |      */
 21 |     async getOptimizedTurns(platform: string, channelId: string): Promise<Turn[]> {
 22 |         // 1. 获取所有回合，按时间倒序
 23 |         const allTurns = await this.ctx.database.select("turns").where({ platform, channelId }).orderBy("startTimestamp", "desc").execute();
 24 |
 25 |         if (allTurns.length === 0) {
 26 |             return [];
 27 |         }
 28 |
 29 |         // 2. 分类回合：活跃回合 vs 过期回合
 30 |         const activeRetentionTime = this.config.TurnManagement.Lifecycle.ActiveRetentionHours * 60 * 60 * 1000;
 31 |         const now = Date.now();
 32 |
 33 |         const activeTurns: TurnData[] = [];
 34 |         const expiredTurns: TurnData[] = [];
 35 |
 36 |         for (const turn of allTurns) {
 37 |             const turnAge = now - turn.startTimestamp.getTime();
 38 |             if (turnAge <= activeRetentionTime && activeTurns.length < this.config.TurnManagement.Lifecycle.MaxActiveTurns) {
 39 |                 activeTurns.push(turn);
 40 |             } else {
 41 |                 expiredTurns.push(turn);
 42 |             }
 43 |         }
 44 |
 45 |         // 3. 处理需要自动折叠的过期回合
 46 |         if (this.config.TurnManagement.Lifecycle.AutoFoldExpired) {
 47 |             await this.autoFoldExpiredTurns(expiredTurns);
 48 |         }
 49 |
 50 |         // 4. 处理需要摘要的回合
 51 |         if (this.config.TurnManagement.Summary.Enabled) {
 52 |             await this.autoSummarizeTurns([...activeTurns, ...expiredTurns]);
 53 |         }
 54 |
 55 |         // 5. 构建优化后的回合对象
 56 |         const optimizedTurns: Turn[] = [];
 57 |
 58 |         // 添加活跃回合（完整显示）
 59 |         for (const turnData of activeTurns) {
 60 |             const fullTurn = await this.buildFullTurn(turnData);
 61 |             optimizedTurns.push(fullTurn);
 62 |         }
 63 |
 64 |         // 添加部分折叠的过期回合（保留关键信息但限制事件数量）
 65 |         const maxFoldedTurns = Math.max(0, this.config.Rendering.MaxTurnsPerChannel - activeTurns.length);
 66 |         for (let i = 0; i < Math.min(expiredTurns.length, maxFoldedTurns); i++) {
 67 |             const turnData = expiredTurns[i];
 68 |             if (turnData.status === "folded") {
 69 |                 const foldedTurn = await this.buildFoldedTurn(turnData);
 70 |                 optimizedTurns.push(foldedTurn);
 71 |             } else {
 72 |                 const fullTurn = await this.buildFullTurn(turnData);
 73 |                 optimizedTurns.push(fullTurn);
 74 |             }
 75 |         }
 76 |
 77 |         return optimizedTurns;
 78 |     }
 79 |
 80 |     /**
 81 |      * 自动折叠过期回合
 82 |      */
 83 |     private async autoFoldExpiredTurns(expiredTurns: TurnData[]): Promise<void> {
 84 |         const turnsToFold = expiredTurns.filter((turn) => turn.status === "full" || turn.status === "new");
 85 |
 86 |         if (turnsToFold.length === 0) {
 87 |             return;
 88 |         }
 89 |
 90 |         this.logger.debug(`自动折叠 ${turnsToFold.length} 个过期回合`);
 91 |
 92 |         for (const turn of turnsToFold) {
 93 |             try {
 94 |                 await this.foldTurn(turn.id);
 95 |             } catch (error) {
 96 |                 this.logger.warn(`折叠回合 ${turn.id} 失败:`, error);
 97 |             }
 98 |         }
 99 |     }
100 |
101 |     /**
102 |      * 自动摘要符合条件的回合
103 |      */
104 |     private async autoSummarizeTurns(turns: TurnData[]): Promise<void> {
105 |         const turnsToSummarize = await this.identifyTurnsForSummary(turns);
106 |
107 |         if (turnsToSummarize.length === 0) {
108 |             return;
109 |         }
110 |
111 |         this.logger.debug(`自动摘要 ${turnsToSummarize.length} 个回合`);
112 |
113 |         for (const turn of turnsToSummarize) {
114 |             try {
115 |                 await this.generateTurnSummary(turn.id);
116 |             } catch (error) {
117 |                 this.logger.warn(`摘要回合 ${turn.id} 失败:`, error);
118 |             }
119 |         }
120 |     }
121 |
122 |     /**
123 |      * 识别需要摘要的回合
124 |      */
125 |     private async identifyTurnsForSummary(turns: TurnData[]): Promise<TurnData[]> {
126 |         const turnsToSummarize: TurnData[] = [];
127 |         const eventCountThreshold = this.config.TurnManagement.Summary.EventCountThreshold;
128 |         const timeThresholdMs = this.config.TurnManagement.Summary.TimeThresholdHours * 60 * 60 * 1000;
129 |         const now = Date.now();
130 |
131 |         for (const turn of turns) {
132 |             // 跳过已经摘要的回合
133 |             if (turn.status === "summarized") {
134 |                 continue;
135 |             }
136 |
137 |             // 检查时间条件
138 |             const turnAge = now - turn.startTimestamp.getTime();
139 |             const meetsTimeThreshold = turnAge >= timeThresholdMs;
140 |
141 |             // 检查事件数量条件
142 |             const eventCount = await this.ctx.database
143 |                 .select("channel_events")
144 |                 .where({ turnId: turn.id })
145 |                 .execute()
146 |                 .then((events) => events.length);
147 |
148 |             const meetsEventThreshold = eventCount >= eventCountThreshold;
149 |
150 |             if (meetsTimeThreshold || meetsEventThreshold) {
151 |                 turnsToSummarize.push(turn);
152 |             }
153 |         }
154 |
155 |         return turnsToSummarize;
156 |     }
157 |
158 |     /**
159 |      * 折叠回合（保留最重要的事件）
160 |      */
161 |     private async foldTurn(turnId: string): Promise<void> {
162 |         // 获取回合的所有事件
163 |         const events = await this.ctx.database.select("channel_events").where({ turnId }).orderBy("timestamp", "asc").execute();
164 |
165 |         if (events.length <= 10) {
166 |             // 事件较少时，仅更新状态
167 |             await this.ctx.database.upsert("turns", [
168 |                 {
169 |                     id: turnId,
170 |                     status: "folded",
171 |                 },
172 |             ]);
173 |             return;
174 |         }
175 |
176 |         // 保留关键事件：开始和结束的几个事件，以及重要事件类型
177 |         const importantEventTypes = ["user_joined", "user_left", "system_notification"];
178 |         const keepEvents = [
179 |             ...events.slice(0, 3), // 前3个事件
180 |             ...events.slice(-3), // 后3个事件
181 |             ...events.filter((event) => importantEventTypes.includes(event.type)),
182 |         ];
183 |
184 |         // 去重（按事件ID）
185 |         const uniqueKeepEvents = Array.from(new Map(keepEvents.map((event) => [event.id, event])).values());
186 |
187 |         // 删除不需要保留的事件
188 |         const keepEventIds = uniqueKeepEvents.map((e) => e.id);
189 |         const eventsToDelete = events.filter((e) => !keepEventIds.includes(e.id));
190 |
191 |         if (eventsToDelete.length > 0) {
192 |             await this.ctx.database.remove("channel_events", {
193 |                 id: eventsToDelete.map((e) => e.id),
194 |             });
195 |         }
196 |
197 |         // 更新回合状态
198 |         await this.ctx.database.upsert("turns", [
199 |             {
200 |                 id: turnId,
201 |                 status: "folded",
202 |             },
203 |         ]);
204 |
205 |         this.logger.debug(`回合 ${turnId} 已折叠，保留 ${uniqueKeepEvents.length} 个关键事件，删除 ${eventsToDelete.length} 个事件`);
206 |     }
207 |
208 |     /**
209 |      * 生成回合摘要
210 |      */
211 |     private async generateTurnSummary(turnId: string): Promise<void> {
212 |         // 获取回合的所有事件和响应
213 |         const [events, responses] = await Promise.all([
214 |             this.ctx.database.get("channel_events", { turnId }),
215 |             this.ctx.database.get("agent_responses", { turnId }),
216 |         ]);
217 |
218 |         // 构建摘要内容
219 |         const summary = this.buildTurnSummary(events, responses);
220 |
221 |         // 更新回合状态和摘要
222 |         await this.ctx.database.upsert("turns", [
223 |             {
224 |                 id: turnId,
225 |                 status: "summarized",
226 |                 summary: summary,
227 |             },
228 |         ]);
229 |
230 |         this.logger.debug(`回合 ${turnId} 已生成摘要`);
231 |     }
232 |
233 |     /**
234 |      * 构建回合摘要
235 |      */
236 |     private buildTurnSummary(events: any[], responses: any[]): string {
237 |         const summaryParts: string[] = [];
238 |
239 |         // 统计事件类型
240 |         const eventTypeCounts = new Map<string, number>();
241 |         for (const event of events) {
242 |             eventTypeCounts.set(event.type, (eventTypeCounts.get(event.type) || 0) + 1);
243 |         }
244 |
245 |         // 添加事件统计
246 |         if (eventTypeCounts.size > 0) {
247 |             const eventStats = Array.from(eventTypeCounts.entries())
248 |                 .map(([type, count]) => `${type}: ${count}`)
249 |                 .join(", ");
250 |             summaryParts.push(`事件: ${eventStats}`);
251 |         }
252 |
253 |         // 添加响应统计
254 |         if (responses.length > 0) {
255 |             summaryParts.push(`AI响应: ${responses.length} 次`);
256 |         }
257 |
258 |         // 时间范围
259 |         if (events.length > 0) {
260 |             const startTime = new Date(Math.min(...events.map((e) => new Date(e.timestamp).getTime())));
261 |             const endTime = new Date(Math.max(...events.map((e) => new Date(e.timestamp).getTime())));
262 |             const duration = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60)); // 分钟
263 |             summaryParts.push(`持续时间: ${duration} 分钟`);
264 |         }
265 |
266 |         return summaryParts.join(" | ");
267 |     }
268 |
269 |     /**
270 |      * 构建完整回合对象
271 |      */
272 |     private async buildFullTurn(turnData: TurnData): Promise<Turn> {
273 |         const [events, responses] = await Promise.all([
274 |             this.ctx.database
275 |                 .select("channel_events")
276 |                 .where({ turnId: turnData.id })
277 |                 .orderBy("timestamp", "asc")
278 |                 .limit(this.config.Rendering.MaxEventsPerTurn)
279 |                 .execute(),
280 |             this.ctx.database.get("agent_responses", { turnId: turnData.id }),
281 |         ]);
282 |
283 |         return {
284 |             id: turnData.id,
285 |             status: turnData.status,
286 |             summary: turnData.summary,
287 |             events: await this.hydrateEvents(events),
288 |             responses: this.hydrateResponses(responses),
289 |         };
290 |     }
291 |
292 |     /**
293 |      * 构建折叠回合对象（限制事件数量）
294 |      */
295 |     private async buildFoldedTurn(turnData: TurnData): Promise<Turn> {
296 |         // 对于折叠的回合，只获取少量关键事件
297 |         const events = await this.ctx.database
298 |             .select("channel_events")
299 |             .where({ turnId: turnData.id })
300 |             .orderBy("timestamp", "asc")
301 |             .limit(5) // 折叠回合只显示5个事件
302 |             .execute();
303 |
304 |         const responses = await this.ctx.database.get("agent_responses", { turnId: turnData.id });
305 |
306 |         return {
307 |             id: turnData.id,
308 |             status: turnData.status,
309 |             summary: turnData.summary || "已折叠的回合",
310 |             events: await this.hydrateEvents(events),
311 |             responses: this.hydrateResponses(responses),
312 |         };
313 |     }
314 |
315 |     /**
316 |      * 填充事件数据（从数据库记录转换为业务对象）
317 |      */
318 |     private async hydrateEvents(eventRecords: any[]): Promise<any[]> {
319 |         // 这里可以复用 TurnRepository 中的逻辑
320 |         // 为了简化，先返回基础转换
321 |         return eventRecords.map((record) => ({
322 |             id: record.id,
323 |             type: record.type,
324 |             timestamp: record.timestamp,
325 |             ...record.data,
326 |         }));
327 |     }
328 |
329 |     /**
330 |      * 填充响应数据
331 |      */
332 |     private hydrateResponses(responseRecords: any[]): any[] {
333 |         return responseRecords.map((record) => ({
334 |             thoughts: record.thoughts || { obverse: "", analyze_infer: "", plan: "" },
335 |             actions: record.actions || [],
336 |             observations: record.observations || [],
337 |         }));
338 |     }
339 |
340 |     /**
341 |      * 手动折叠指定回合
342 |      */
343 |     async manualFoldTurn(turnId: string): Promise<void> {
344 |         await this.foldTurn(turnId);
345 |         this.logger.info(`手动折叠回合 ${turnId}`);
346 |     }
347 |
348 |     /**
349 |      * 手动生成回合摘要
350 |      */
351 |     async manualSummarizeTurn(turnId: string): Promise<void> {
352 |         await this.generateTurnSummary(turnId);
353 |         this.logger.info(`手动生成回合 ${turnId} 的摘要`);
354 |     }
355 |
356 |     /**
357 |      * 获取回合统计信息
358 |      */
359 |     async getTurnStatistics(
360 |         platform: string,
361 |         channelId: string
362 |     ): Promise<{
363 |         total: number;
364 |         active: number;
365 |         folded: number;
366 |         summarized: number;
367 |     }> {
368 |         const turns = await this.ctx.database.get("turns", { platform, channelId });
369 |
370 |         return {
371 |             total: turns.length,
372 |             active: turns.filter((t) => t.status === "new" || t.status === "full").length,
373 |             folded: turns.filter((t) => t.status === "folded").length,
374 |             summarized: turns.filter((t) => t.status === "summarized").length,
375 |         };
376 |     }
377 | }

</file_content>
</file_content>

<file_content path="packages/core/src/services/worldstate/repositories">
├── MemberRepository.ts
└── TurnRepository.ts

<file_content path="packages/core/src/services/worldstate/repositories/MemberRepository.ts">
 1 | import { Context } from "koishi";
 2 | import { Member } from "../interfaces";
 3 |
 4 | export class MemberRepository {
 5 |     constructor(private ctx: Context) {}
 6 |
 7 |     /**
 8 |      * 根据一组平台用户ID (pids)，高效地获取他们在一个频道中的完整 Member 对象。
 9 |      * @param platform - 平台名称
10 |      * @param channelId - 频道ID
11 |      * @param pids - 平台用户ID (如QQ号) 的数组
12 |      * @returns 返回一个包含完整 Member 信息的数组
13 |      */
14 |     async getFullMembersByPids(platform: string, channelId: string, pids: string[]): Promise<Member[]> {
15 |         // 如果传入的 pids 数组为空，直接返回空数组，避免无效的数据库查询
16 |         if (!pids || pids.length === 0) {
17 |             return [];
18 |         }
19 |
20 |         // --- 第 1 步: 从 pids 找到内部 bids) from platform IDs (pids).
21 |         const bindingRecords = await this.ctx.database.get("binding", { platform, pid: pids });
22 |         const bids = bindingRecords.map((b) => b.bid);
23 |         if (bids.length === 0) {
24 |             // 如果没有找到任何匹配的内部用户，也直接返回
25 |             return [];
26 |         }
27 |
28 |         // 创建一个 bid -> pid 的反向映射，方便后续组装
29 |         const bidToPidMap = new Map(bindingRecords.map((b) => [b.bid, b.pid]));
30 |
31 |         // --- 第 2 步: 使用 bids 并行查询 user 和 member 表 ---
32 |         const [userRecords, memberRecords] = await Promise.all([
33 |             this.ctx.database.get("user", { id: bids }),
34 |             this.ctx.database.get("members", { userId: bids, platform, channelId }),
35 |         ]);
36 |
37 |         // --- 第 3 步: 在内存中高效组合数据 ---
38 |
39 |         // 创建 Map 以便快速查找，这是避免 O(n^2) 循环的关键
40 |         const userMap = new Map(userRecords.map((u) => [u.id, u]));
41 |         const memberMap = new Map(memberRecords.map((m) => [m.userId, m]));
42 |
43 |         const result: Member[] = [];
44 |
45 |         // 遍历我们找到的内部 bids，而不是原始的 pids，因为有些 pid 可能不存在
46 |         for (const bid of bids) {
47 |             const userRecord = userMap.get(bid);
48 |             const memberRecord = memberMap.get(bid);
49 |             const pid = bidToPidMap.get(bid);
50 |
51 |             // 必须要有对应的 user 记录和 binding 记录才能构成一个有效的 Member
52 |             if (!userRecord || !pid) {
53 |                 continue;
54 |             }
55 |
56 |             // memberRecord 是可选的，如果一个用户存在但从未在该频道发言或被记录，
57 |             // 他可能没有 member 记录。我们可以创建一个默认的。
58 |             const nick = memberRecord?.nick ?? userRecord.name;
59 |             const role = memberRecord?.role;
60 |             const lastActive = memberRecord?.lastActive;
61 |
62 |             result.push({
63 |                 // User 部分
64 |                 id: pid, // 对外暴露的是平台ID
65 |                 name: userRecord.name ?? "未知用户",
66 |                 created_at: userRecord.createdAt,
67 |                 updated_at: userRecord.updatedAt,
68 |
69 |                 // Member 特有部分
70 |                 channel_id: channelId,
71 |                 last_active: lastActive?.toISOString(),
72 |                 meta: {
73 |                     avatar: userRecord.avatar,
74 |                     nick: nick,
75 |                     role: role,
76 |                 },
77 |             });
78 |         }
79 |         return result;
80 |     }
81 |
82 |     /**
83 |      * 获取指定频道的所有成员的完整信息 (可以复用 getFullMembersByPids)
84 |      */
85 |     async getFullMembers(platform: string, channelId: string): Promise<Member[]> {
86 |         // 获取该频道所有成员的记录
87 |         const memberRecords = await this.ctx.database.get("members", { platform, channelId });
88 |         const bids = memberRecords.map((m) => m.userId);
89 |         if (bids.length === 0) return [];
90 |
91 |         // 获取这些内部ID对应的平台ID
92 |         const bindingRecords = await this.ctx.database.get("binding", { bid: bids, platform });
93 |         const pids = bindingRecords.map((b) => b.pid);
94 |
95 |         return this.getFullMembersByPids(platform, channelId, pids);
96 |     }
97 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/repositories/TurnRepository.ts">
  1 | import { Context } from "koishi";
  2 | import {
  3 |     Action,
  4 |     ActionResult,
  5 |     AgentResponse,
  6 |     ChannelEvent,
  7 |     MessageEvent,
  8 |     SystemNotificationEvent,
  9 |     Turn,
 10 |     UserJoinedEvent,
 11 |     UserLeftEvent,
 12 | } from "../interfaces";
 13 | import { AgentResponseData, TurnData } from "../model";
 14 | import { MemberRepository } from "./MemberRepository";
 15 |
 16 | export class TurnRepository {
 17 |     constructor(private ctx: Context, private memberRepo: MemberRepository) {}
 18 |
 19 |     async getFullTurns(platform: string, channelId: string): Promise<Turn[]> {
 20 |         const turnRecords = await this.ctx.database.get("turns", { platform, channelId });
 21 |         if (!turnRecords.length) return [];
 22 |         return Promise.all(turnRecords.map((turn) => this.buildFullTurn(turn, platform, channelId)));
 23 |     }
 24 |
 25 |     private async buildFullTurn(turnRecord: TurnData, platform: string, channelId: string): Promise<Turn> {
 26 |         // 1.1 获取此 Turn 下的所有原始事件记录，并按时间戳排序
 27 |         // 1.2 获取此 Turn 下的所有 AI 响应
 28 |         const [eventRecords, responseRecords] = await Promise.all([
 29 |             this.ctx.database.get("channel_events", { turnId: turnRecord.id }, { sort: { timestamp: "asc" } }),
 30 |             this.ctx.database.get("agent_responses", { turnId: turnRecord.id }),
 31 |         ]);
 32 |
 33 |         // 2. Build the AgentResponse array.
 34 |         const responses = this.buildAgentResponses(responseRecords);
 35 |
 36 |         // 3. 一次性获取所有事件中涉及到的成员信息
 37 |         const allMemberPids = new Set<string>();
 38 |         for (const event of eventRecords) {
 39 |             const data = event.data as any; // 临时转为 any 以便访问属性
 40 |             if (data.actorId) allMemberPids.add(data.actorId);
 41 |             if (data.userId) allMemberPids.add(data.userId);
 42 |             if (data.senderId) allMemberPids.add(data.senderId);
 43 |         }
 44 |
 45 |         // 使用 MemberRepository 获取完整的 Member 对象，并建立一个 Map 以便快速查找
 46 |         const memberMap = new Map(
 47 |             (await this.memberRepo.getFullMembersByPids(platform, channelId, Array.from(allMemberPids))).map((m) => [m.id, m])
 48 |         );
 49 |
 50 |         // 4. 动态构建事件数组 (Hydration)
 51 |         const events: ChannelEvent[] = eventRecords
 52 |             .map((record) => {
 53 |                 const data = record.data as any;
 54 |                 const base = { id: record.id, timestamp: record.timestamp };
 55 |                 const systemActor = { id: "system", name: "System", meta: {} }; // 默认系统角色
 56 |
 57 |                 switch (record.type) {
 58 |                     case "user_joined":
 59 |                         return {
 60 |                             ...base,
 61 |                             type: "user_joined",
 62 |                             actor: memberMap.get(data.actorId) ?? systemActor,
 63 |                             user: memberMap.get(data.userId) ?? { id: data.userId, name: "未知用户" },
 64 |                             note: data.note,
 65 |                         } as UserJoinedEvent;
 66 |                     case "user_left":
 67 |                         return {
 68 |                             ...base,
 69 |                             type: "user_left",
 70 |                             actor: memberMap.get(data.actorId) ?? systemActor,
 71 |                             user: memberMap.get(data.userId) ?? { id: data.userId, name: "未知用户" },
 72 |                             reason: data.reason,
 73 |                         } as UserLeftEvent;
 74 |                     case "message":
 75 |                     case "message_sent":
 76 |                         return {
 77 |                             ...base,
 78 |                             type: "message",
 79 |                             messageId: data.messageId,
 80 |                             sender: memberMap.get(data.senderId) ?? { id: data.senderId, name: "未知用户" },
 81 |                             content: data.content,
 82 |                         } as MessageEvent;
 83 |                     case "system_notification":
 84 |                         return { ...base, type: "system_notification", content: data.content } as SystemNotificationEvent;
 85 |                     default:
 86 |                         return null;
 87 |                 }
 88 |             })
 89 |             .filter(Boolean)
 90 |             .map((event) => {
 91 |                 // 创建一个新对象，避免修改原始数据
 92 |                 const templateEvent: any = { ...event };
 93 |
 94 |                 // 根据事件类型添加布尔标记
 95 |                 templateEvent[`is_${event.type}`] = true;
 96 |
 97 |                 return templateEvent;
 98 |             });
 99 |
100 |         return {
101 |             id: turnRecord.id,
102 |             status: turnRecord.status,
103 |             summary: turnRecord.summary,
104 |             responses,
105 |             events,
106 |         };
107 |     }
108 |
109 |     /**
110 |      * 将数据库中的 AgentResponseData 记录转换为业务层的 AgentResponse 对象数组。
111 |      * @param records - 从 agent_responses 表查询出的记录数组
112 |      * @returns - 符合 AgentResponse 接口定义的对象数组
113 |      */
114 |     private buildAgentResponses(records: AgentResponseData[]): AgentResponse[] {
115 |         if (!records || records.length === 0) return [];
116 |         return records.map((record) => ({
117 |             thoughts: this.validateThoughts(record.thoughts),
118 |             actions: this.validateActions(record.actions),
119 |             observations: this.validateObservations(record.observations),
120 |         }));
121 |     }
122 |
123 |     // --- Optional validation methods for robustness ---
124 |     private validateThoughts(data: any): AgentResponse["thoughts"] {
125 |         if (typeof data !== "object" || data === null) return { obverse: "", analyze_infer: "", plan: "" };
126 |         return {
127 |             obverse: typeof data.obverse === "string" ? data.obverse : "",
128 |             analyze_infer: typeof data.analyze_infer === "string" ? data.analyze_infer : "",
129 |             plan: typeof data.plan === "string" ? data.plan : "",
130 |         };
131 |     }
132 |
133 |     private validateActions(data: any): Action[] {
134 |         if (!Array.isArray(data)) return [];
135 |         return data
136 |             .filter((item) => typeof item === "object" && item !== null && typeof item.function === "string")
137 |             .map((item) => ({
138 |                 function: item.function,
139 |                 params: typeof item.params === "object" && item.params !== null ? item.params : {},
140 |                 renderParams() {
141 |                     return JSON.stringify(this.params);
142 |                 },
143 |             }));
144 |     }
145 |
146 |     private validateObservations(data: any): ActionResult[] {
147 |         if (!Array.isArray(data)) return [];
148 |         const observations = data.filter((item) => typeof item === "object" && item !== null) as ActionResult[];
149 |         observations.forEach((item) => {
150 |             item.renderResult = function () {
151 |                 const result = this.result?.result || this.result?.error || "";
152 |                 return typeof result === "string" ? result : JSON.stringify(result);
153 |             };
154 |         });
155 |         return observations;
156 |     }
157 | }

</file_content>
</file_content>
