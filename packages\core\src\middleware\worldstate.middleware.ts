import { Context, Session } from "koishi";
import { BaseMiddleware, MiddlewareContext } from "./base";
import { DataManager, MessageClassifier, MessageClassification } from "../services/worldstate";

/**
 * WorldState 中间件配置
 */
export interface WorldStateMiddlewareConfig {
    /**
     * 是否启用消息分类
     */
    EnableMessageClassification: boolean;

    /**
     * 是否启用自动事件记录
     */
    EnableAutoEventRecording: boolean;

    /**
     * 是否在回合开始时记录系统事件
     */
    RecordTurnStartEvent: boolean;

    /**
     * 是否在回合结束时记录系统事件
     */
    RecordTurnEndEvent: boolean;

    /**
     * 错误处理策略
     */
    ErrorHandling: "continue" | "stop";
}

/**
 * WorldState 中间件
 *
 * 职责：
 * 1. 在中间件管道早期处理消息分类和事件记录
 * 2. 确保回合的正确开始和结束时机
 * 3. 为后续中间件提供分类信息
 * 4. 协调与 ReasoningMiddleware 的工作
 */
export class WorldStateMiddleware extends BaseMiddleware<WorldStateMiddlewareConfig> {
    private readonly dataManager: DataManager;
    private readonly messageClassifier: MessageClassifier;

    constructor(ctx: Context, config: WorldStateMiddlewareConfig) {
        super("worldstate", ctx, config);

        // 获取服务实例
        const dataManager = this.ctx.get("yesimbot.data");
        if (!dataManager) {
            throw new Error("WorldStateMiddleware 初始化失败：缺少 DataManager 服务");
        }
        this.dataManager = dataManager;

        // 从 DataManager 获取 MessageClassifier
        const messageClassifier = (this.dataManager as any).messageClassifier;
        if (!messageClassifier) {
            throw new Error("WorldStateMiddleware 初始化失败：DataManager 中缺少 MessageClassifier");
        }
        this.messageClassifier = messageClassifier;
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const session = ctx.koishiSession;

        try {
            // 1. 处理消息分类（如果启用）
            let classification: MessageClassification | undefined;
            if (this.config.EnableMessageClassification) {
                classification = this.messageClassifier.classifyMessage(session);
                this.setShared(ctx, "messageClassification", classification);

                this.logger.debug(
                    `消息分类完成: 类型=${classification.type}, 分类=${classification.category}, 优先级=${classification.priority}`
                );
            }

            // 2. 处理自动事件记录（如果启用且消息应该被存储）
            if (this.config.EnableAutoEventRecording && classification?.shouldStore) {
                await this.recordMessageEvent(ctx, session, classification);
            }

            // 3. 记录回合开始事件（如果这是新回合的第一个消息）
            if (this.config.RecordTurnStartEvent) {
                await this.recordTurnStartIfNeeded(ctx, session);
            }

            // 4. 继续执行后续中间件
            await next();

            // 5. 处理回合结束逻辑（在所有中间件执行完毕后）
            await this.handleTurnCompletion(ctx, session);
        } catch (error) {
            this.logger.error("WorldState 中间件执行失败:", error);

            if (this.config.ErrorHandling === "stop") {
                throw error;
            }

            // 如果配置为继续执行，则继续后续中间件
            await next();
        }
    }

    /**
     * 记录消息事件
     */
    private async recordMessageEvent(ctx: MiddlewareContext, session: Session, classification: MessageClassification): Promise<void> {
        try {
            const eventData = await this.dataManager.addMessageEvent(ctx.currentTurnId, session);

            if (eventData) {
                this.setShared(ctx, "eventCreated", true);
                this.setShared(ctx, "eventData", eventData);
                this.logger.debug(`消息事件已记录: ID=${eventData.id}, 类型=${eventData.type}`);
            } else {
                this.setShared(ctx, "eventCreated", false);
                this.logger.debug("消息被过滤，未创建事件记录");
            }
        } catch (error) {
            this.logger.error("记录消息事件失败:", error);
        }
    }

    /**
     * 记录回合开始事件（如果需要）
     */
    private async recordTurnStartIfNeeded(ctx: MiddlewareContext, session: Session): Promise<void> {
        try {
            // 检查当前回合是否刚刚创建
            const turnData = await this.dataManager.getLastTurn(session.platform, session.channelId);

            if (turnData && turnData.id === ctx.currentTurnId) {
                // 检查这个回合是否还没有事件（表示是新创建的）
                const existingEvents = await this.ctx.database.get("channel_events", { turnId: turnData.id });

                if (existingEvents.length === 0) {
                    // 记录回合开始事件
                    await this.dataManager.addGenericEvent(turnData.id, "turn_started", {
                        platform: session.platform,
                        channelId: session.channelId,
                        startTime: turnData.startTimestamp,
                        trigger: "user_message",
                    });

                    this.logger.debug(`回合开始事件已记录: 回合=${turnData.id}`);
                }
            }
        } catch (error) {
            this.logger.error("记录回合开始事件失败:", error);
        }
    }

    /**
     * 处理回合完成逻辑
     */
    private async handleTurnCompletion(ctx: MiddlewareContext, session: Session): Promise<void> {
        try {
            // 检查是否应该结束当前回合
            const shouldEndTurn = this.shouldEndCurrentTurn(ctx, session);

            if (shouldEndTurn) {
                await this.endCurrentTurn(ctx, session);
            }
        } catch (error) {
            this.logger.error("处理回合完成逻辑失败:", error);
        }
    }

    /**
     * 判断是否应该结束当前回合
     */
    private shouldEndCurrentTurn(ctx: MiddlewareContext, session: Session): boolean {
        // 获取分类信息
        const classification = this.getShared<MessageClassification>(ctx, "messageClassification");

        // 如果没有 Agent 响应，说明没有进行推理，不需要结束回合
        if (!ctx.agentResponses || ctx.agentResponses.length === 0) {
            return false;
        }

        // 如果最后一个 Agent 响应没有请求继续心跳，则结束回合
        const lastResponse = ctx.agentResponses[ctx.agentResponses.length - 1];
        // 这里需要根据实际的响应结构来判断是否继续
        // 暂时使用简单的逻辑：如果有响应就结束回合
        return true;
    }

    /**
     * 结束当前回合
     */
    private async endCurrentTurn(ctx: MiddlewareContext, session: Session): Promise<void> {
        try {
            // 记录回合结束事件（如果启用）
            if (this.config.RecordTurnEndEvent) {
                await this.dataManager.addGenericEvent(ctx.currentTurnId, "turn_ended", {
                    platform: session.platform,
                    channelId: session.channelId,
                    endTime: new Date(),
                    agentResponseCount: ctx.agentResponses?.length || 0,
                    reason: "reasoning_completed",
                });
            }

            // 生成回合摘要（基于当前的事件和响应）
            const summary = this.generateTurnSummary(ctx);

            // 正式结束回合
            await this.dataManager.endTurn(ctx.currentTurnId, summary);

            this.logger.info(`回合已结束: ${ctx.currentTurnId}, 摘要: ${summary}`);
        } catch (error) {
            this.logger.error("结束回合失败:", error);
        }
    }

    /**
     * 生成回合摘要
     */
    private generateTurnSummary(ctx: MiddlewareContext): string {
        const parts: string[] = [];

        // 添加 Agent 响应统计
        if (ctx.agentResponses && ctx.agentResponses.length > 0) {
            parts.push(`AI响应: ${ctx.agentResponses.length} 次`);

            // 统计工具调用
            const totalActions = ctx.agentResponses.reduce((sum, response) => sum + (response.actions?.length || 0), 0);
            if (totalActions > 0) {
                parts.push(`工具调用: ${totalActions} 次`);
            }
        }

        // 添加消息分类信息
        const classification = this.getShared<MessageClassification>(ctx, "messageClassification");
        if (classification) {
            parts.push(`消息类型: ${classification.type}`);
        }

        // 添加时间信息
        const now = new Date();
        parts.push(`完成时间: ${now.toISOString()}`);

        return parts.join(" | ") || "空回合";
    }

    /**
     * 获取消息分类结果（供其他中间件使用）
     */
    public static getMessageClassification(ctx: MiddlewareContext): MessageClassification | undefined {
        return ctx.shared.get("messageClassification");
    }

    /**
     * 检查是否创建了事件记录（供其他中间件使用）
     */
    public static isEventCreated(ctx: MiddlewareContext): boolean {
        return ctx.shared.get("eventCreated") || false;
    }

    /**
     * 获取创建的事件数据（供其他中间件使用）
     */
    public static getEventData(ctx: MiddlewareContext): any {
        return ctx.shared.get("eventData");
    }
}

/**
 * 默认配置
 */
export const defaultWorldStateMiddlewareConfig: WorldStateMiddlewareConfig = {
    EnableMessageClassification: true,
    EnableAutoEventRecording: true,
    RecordTurnStartEvent: true,
    RecordTurnEndEvent: true,
    ErrorHandling: "continue",
};
