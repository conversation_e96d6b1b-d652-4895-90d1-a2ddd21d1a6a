import { Schema } from "koishi";

export interface WorldStateConfig {
    /**
     * 数据管理配置
     */
    DataManagement: {
        /**
         * 成员信息更新配置
         */
        MemberUpdate: {
            /**
             * 是否启用定期成员信息更新
             */
            Enabled: boolean;
            /**
             * 更新间隔（分钟）
             */
            IntervalMinutes: number;
            /**
             * 批量更新大小
             */
            BatchSize: number;
        };

        /**
         * 群组信息更新配置
         */
        ChannelUpdate: {
            /**
             * 是否启用定期群组信息更新
             */
            Enabled: boolean;
            /**
             * 更新间隔（分钟）
             */
            IntervalMinutes: number;
        };
    };

    /**
     * 回合管理配置
     */
    TurnManagement: {
        /**
         * 回合生命周期配置
         */
        Lifecycle: {
            /**
             * 活跃回合保留时间（小时）
             */
            ActiveRetentionHours: number;
            /**
             * 最大保留回合数
             */
            MaxActiveTurns: number;
            /**
             * 自动折叠过期回合
             */
            AutoFoldExpired: boolean;
        };

        /**
         * 回合摘要配置
         */
        Summary: {
            /**
             * 是否启用自动摘要
             */
            Enabled: boolean;
            /**
             * 触发摘要的事件数阈值
             */
            EventCountThreshold: number;
            /**
             * 触发摘要的时间阈值（小时）
             */
            TimeThresholdHours: number;
        };
    };

    /**
     * 消息处理配置
     */
    MessageProcessing: {
        /**
         * 消息分类配置
         */
        Classification: {
            /**
             * 用户消息关键词
             */
            UserMessageKeywords: string[];
            /**
             * 系统事件关键词
             */
            SystemEventKeywords: string[];
        };

        /**
         * 消息过滤配置
         */
        Filtering: {
            /**
             * 最小消息长度
             */
            MinMessageLength: number;
            /**
             * 忽略的消息类型
             */
            IgnoredMessageTypes: string[];
        };
    };

    /**
     * 数据清理配置
     */
    DataCleanup: {
        /**
         * 是否启用定期清理
         */
        Enabled: boolean;
        /**
         * 清理间隔（小时）
         */
        IntervalHours: number;
        /**
         * 数据保留期限（天）
         */
        RetentionDays: number;
        /**
         * 清理批量大小
         */
        BatchSize: number;
    };

    /**
     * 渲染配置
     */
    Rendering: {
        /**
         * 活跃频道显示限制
         */
        ActiveChannelLimit: number;
        /**
         * 每个频道显示的最大回合数
         */
        MaxTurnsPerChannel: number;
        /**
         * 每个回合显示的最大事件数
         */
        MaxEventsPerTurn: number;
    };
}

export const WorldStateConfigSchema: Schema<WorldStateConfig> = Schema.object({
    DataManagement: Schema.object({
        MemberUpdate: Schema.object({
            Enabled: Schema.boolean().default(true).description("是否启用定期成员信息更新"),
            IntervalMinutes: Schema.number().min(5).max(1440).default(60).description("更新间隔（分钟）"),
            BatchSize: Schema.number().min(10).max(1000).default(100).description("批量更新大小"),
        }).description("成员信息更新配置"),

        ChannelUpdate: Schema.object({
            Enabled: Schema.boolean().default(true).description("是否启用定期群组信息更新"),
            IntervalMinutes: Schema.number().min(10).max(1440).default(120).description("更新间隔（分钟）"),
        }).description("群组信息更新配置"),
    }).description("数据管理配置"),

    TurnManagement: Schema.object({
        Lifecycle: Schema.object({
            ActiveRetentionHours: Schema.number().min(1).max(168).default(24).description("活跃回合保留时间（小时）"),
            MaxActiveTurns: Schema.number().min(5).max(100).default(20).description("最大保留回合数"),
            AutoFoldExpired: Schema.boolean().default(true).description("自动折叠过期回合"),
        }).description("回合生命周期配置"),

        Summary: Schema.object({
            Enabled: Schema.boolean().default(true).description("是否启用自动摘要"),
            EventCountThreshold: Schema.number().min(10).max(500).default(50).description("触发摘要的事件数阈值"),
            TimeThresholdHours: Schema.number().min(1).max(72).default(12).description("触发摘要的时间阈值（小时）"),
        }).description("回合摘要配置"),
    }).description("回合管理配置"),

    MessageProcessing: Schema.object({
        Classification: Schema.object({
            UserMessageKeywords: Schema.array(String).default(["message_sent", "message", "user_message"]).description("用户消息关键词"),
            SystemEventKeywords: Schema.array(String)
                .default(["user_joined", "user_left", "system_notification", "member_updated"])
                .description("系统事件关键词"),
        }).description("消息分类配置"),

        Filtering: Schema.object({
            MinMessageLength: Schema.number().min(0).max(100).default(1).description("最小消息长度"),
            IgnoredMessageTypes: Schema.array(String).default(["heartbeat", "typing", "presence"]).description("忽略的消息类型"),
        }).description("消息过滤配置"),
    }).description("消息处理配置"),

    DataCleanup: Schema.object({
        Enabled: Schema.boolean().default(true).description("是否启用定期清理"),
        IntervalHours: Schema.number().min(1).max(168).default(24).description("清理间隔（小时）"),
        RetentionDays: Schema.number().min(1).max(365).default(30).description("数据保留期限（天）"),
        BatchSize: Schema.number().min(100).max(10000).default(1000).description("清理批量大小"),
    }).description("数据清理配置"),

    Rendering: Schema.object({
        ActiveChannelLimit: Schema.number().min(1).max(50).default(10).description("活跃频道显示限制"),
        MaxTurnsPerChannel: Schema.number().min(1).max(100).default(10).description("每个频道显示的最大回合数"),
        MaxEventsPerTurn: Schema.number().min(5).max(500).default(50).description("每个回合显示的最大事件数"),
    }).description("渲染配置"),
});
