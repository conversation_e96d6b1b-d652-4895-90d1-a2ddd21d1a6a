# WorldState 上下文管理器模块

## 概述

WorldState 模块是为 LLM chatbot 设计的核心组件，负责收集、管理和组织群组消息、聊天记录、系统事件，并为 PromptBuilder 提供结构化数据用于渲染提示词。

## 模块结构

```
worldstate/
├── config.ts                    # 配置驱动的架构
├── DataManager.ts              # 主要数据管理器（增强版）
├── interfaces.ts               # 接口定义
├── model.ts                    # 数据模型和数据库表定义
├── background-task-manager.ts  # 后台任务管理器
├── turn-manager.ts             # 回合管理器
├── message-classifier.ts       # 消息分类器
├── data-cleanup-service.ts     # 数据清理服务
├── repositories/               # 数据访问层
│   ├── MemberRepository.ts
│   └── TurnRepository.ts
└── README.md                   # 本文档
```

## 已实现的功能改进

### 1. 成员和群组信息更新机制 ✅

**实现位置**: `background-task-manager.ts`

- **定期更新任务**: 可配置的成员信息和群组信息更新间隔
- **批量处理**: 支持批量更新，避免性能问题
- **错误处理**: 优雅处理单个更新失败的情况
- **配置选项**:
  - `DataManagement.MemberUpdate.Enabled`: 是否启用成员更新
  - `DataManagement.MemberUpdate.IntervalMinutes`: 更新间隔（分钟）
  - `DataManagement.MemberUpdate.BatchSize`: 批量大小

### 2. 回合管理优化 ✅

**实现位置**: `turn-manager.ts`

- **自动折叠**: 根据时间和事件数量自动折叠过期回合
- **智能摘要**: 自动生成回合摘要，保持上下文连续性
- **渲染优化**: 限制显示的回合数量和事件数量
- **配置选项**:
  - `TurnManagement.Lifecycle.ActiveRetentionHours`: 活跃回合保留时间
  - `TurnManagement.Lifecycle.MaxActiveTurns`: 最大活跃回合数
  - `TurnManagement.Summary.Enabled`: 是否启用自动摘要

### 3. 消息入库机制改进 ✅

**实现位置**: `message-classifier.ts`

- **智能分类**: 准确区分用户消息、系统事件、机器人响应等
- **过滤机制**: 自动过滤不重要的消息，减少存储开销
- **优先级管理**: 为不同类型的消息分配优先级
- **元数据提取**: 提取消息的详细元数据信息

### 4. 数据清理功能 ✅

**实现位置**: `data-cleanup-service.ts`

- **定期清理**: 可配置的数据清理间隔和保留期限
- **完整性检查**: 检测和修复数据完整性问题
- **存储分析**: 估算存储使用情况和增长趋势
- **批量操作**: 分批处理大量数据，避免性能问题

### 5. 中间件集成 ✅

**实现位置**: `middleware/worldstate-middleware.ts`

- **无缝集成**: 与现有中间件架构协调工作
- **回合时机管理**: 正确处理回合开始和结束时机
- **上下文传递**: 为后续中间件提供分类和状态信息
- **错误处理**: 优雅的错误处理策略

## 配置说明

### 主要配置项

```typescript
interface WorldStateConfig {
  DataManagement: {
    MemberUpdate: {
      Enabled: boolean;           // 是否启用成员更新
      IntervalMinutes: number;    // 更新间隔（分钟）
      BatchSize: number;          // 批量大小
    };
    ChannelUpdate: {
      Enabled: boolean;           // 是否启用群组更新
      IntervalMinutes: number;    // 更新间隔（分钟）
    };
  };
  TurnManagement: {
    Lifecycle: {
      ActiveRetentionHours: number;  // 活跃回合保留时间（小时）
      MaxActiveTurns: number;        // 最大活跃回合数
      AutoFoldExpired: boolean;      // 自动折叠过期回合
    };
    Summary: {
      Enabled: boolean;              // 是否启用自动摘要
      EventCountThreshold: number;   // 触发摘要的事件数阈值
      TimeThresholdHours: number;    // 触发摘要的时间阈值（小时）
    };
  };
  DataCleanup: {
    Enabled: boolean;           // 是否启用定期清理
    IntervalHours: number;      // 清理间隔（小时）
    RetentionDays: number;      // 数据保留期限（天）
    BatchSize: number;          // 清理批量大小
  };
  Rendering: {
    ActiveChannelLimit: number;     // 活跃频道显示限制
    MaxTurnsPerChannel: number;     // 每个频道显示的最大回合数
    MaxEventsPerTurn: number;       // 每个回合显示的最大事件数
  };
}
```

## 使用方式

### 1. 基本使用

```typescript
import { DataManager, WorldStateConfig } from "./services/worldstate";

// 创建配置
const config: WorldStateConfig = {
  // ... 配置项
};

// 创建数据管理器
const dataManager = new DataManager(ctx, config);

// 获取世界状态
const worldState = await dataManager.getWorldState(allowedChannels);
```

### 2. 中间件集成

```typescript
import { WorldStateMiddleware } from "./middleware/worldstate-middleware";

// 创建中间件
const worldStateMiddleware = new WorldStateMiddleware(ctx, {
  enableMessageClassification: true,
  enableAutoEventRecording: true,
  recordTurnStartEvent: true,
  recordTurnEndEvent: true,
  errorHandling: "continue"
});

// 添加到管道
pipeline.use(worldStateMiddleware);
```

### 3. 手动操作

```typescript
// 手动折叠回合
await dataManager.foldTurn(turnId);

// 手动生成摘要
await dataManager.summarizeTurn(turnId);

// 执行数据清理
const cleanupResult = await dataManager.performDataCleanup();

// 获取统计信息
const stats = await dataManager.getDatabaseStats();
```

## 技术特点

### 1. 配置驱动架构
- 所有功能都可通过配置启用/禁用
- 避免硬编码值，提高灵活性
- 支持运行时配置更新

### 2. 模块化设计
- 每个类承担单一职责
- 清晰的依赖关系
- 易于测试和维护

### 3. 性能优化
- 批量处理大量数据
- 智能缓存和索引
- 分页和限制机制

### 4. 错误处理
- 优雅的错误处理策略
- 详细的日志记录
- 故障隔离机制

## 数据库表结构

模块扩展了以下 Koishi 数据库表：

- `user`: 用户基础信息
- `channel`: 频道/群组信息
- `members`: 成员信息
- `turns`: 回合数据
- `channel_events`: 频道事件
- `agent_responses`: AI 响应记录

## 模板渲染

配合 `world_state.mustache` 模板使用，支持：

- 活跃频道和非活跃频道的区分显示
- 回合状态的动态渲染
- 事件类型的条件显示
- 响应数据的结构化输出

## 注意事项

1. **性能考虑**: 大量数据时注意配置合适的批量大小和清理间隔
2. **存储空间**: 定期监控存储使用情况，及时清理过期数据
3. **配置调优**: 根据实际使用情况调整回合保留时间和摘要阈值
4. **错误监控**: 关注后台任务的执行状态和错误日志

## 未来扩展

- 支持更多消息类型的分类
- 实现更智能的回合分割算法
- 添加数据导出和备份功能
- 支持分布式部署的数据同步